"""Main Patreon automation script."""

import os
import sys
import time
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Any
from colorama import init, Fore, Style

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from post_extractor import PostExtractor
from export_controller import ExportController

# Import Playwright
try:
    from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
except ImportError:
    print("Playwright not installed. Please run: pip install playwright && playwright install chromium")
    sys.exit(1)

# Initialize colorama for colored output
init(autoreset=True)


class PatreonAutomation:
    """Main automation class for batch exporting Patreon posts."""
    
    def __init__(self, config_path: str = None):
        """Initialize the automation system.
        
        Args:
            config_path: Path to configuration file
        """
        self.config = Config(config_path)
        self.logger = self._setup_logging()
        self.stats = {
            'total_posts': 0,
            'exported': 0,
            'skipped': 0,
            'failed': 0,
            'start_time': None,
            'end_time': None
        }
        
    def _setup_logging(self) -> logging.Logger:
        """Set up logging configuration."""
        logger = logging.getLogger('patreon_automation')
        logger.setLevel(getattr(logging, self.config.log_level))
        
        # Create logs directory if it doesn't exist
        log_file_path = Path(self.config.log_file)
        log_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # File handler
        file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, self.config.log_level))
        console_formatter = logging.Formatter('%(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        
        # Add handlers
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def run(self, creator_name: str = None, max_posts: int = None, skip_existing: bool = True):
        """Run the automation for specified creators.
        
        Args:
            creator_name: Specific creator to process (None for all enabled)
            max_posts: Maximum posts to process per creator
            skip_existing: Whether to skip already exported posts
        """
        self.stats['start_time'] = time.time()
        
        print(f"{Fore.GREEN}🚀 Starting Patreon Automation{Style.RESET_ALL}")
        print(f"Config: {self.config.config_path}")
        print(f"Output: {self.config.output_directory}")
        print("-" * 50)
        
        try:
            # Validate configuration
            errors = self.config.validate()
            if errors:
                self.logger.error("Configuration errors:")
                for error in errors:
                    self.logger.error(f"  - {error}")
                print(f"{Fore.RED}❌ Configuration errors found. Please fix and try again.{Style.RESET_ALL}")
                return False
            
            # Get creators to process
            creators = self._get_creators_to_process(creator_name)
            if not creators:
                print(f"{Fore.YELLOW}⚠️  No creators to process{Style.RESET_ALL}")
                return False
            
            print(f"Processing {len(creators)} creator(s)...")
            
            # Start browser automation
            with sync_playwright() as p:
                context = self._setup_browser(p)
                
                try:
                    # Get existing page or create new one
                    pages = context.pages
                    if pages:
                        page = pages[0]
                        self.logger.info("Using existing browser page")
                    else:
                        page = context.new_page()
                        self.logger.info("Created new browser page")
                    
                    # Initialize components
                    post_extractor = PostExtractor(page, self.logger)
                    export_controller = ExportController(page, self.config, self.logger)
                    
                    # Process each creator
                    for creator in creators:
                        self._process_creator(
                            creator, post_extractor, export_controller,
                            max_posts, skip_existing
                        )
                    
                finally:
                    # Don't close the context if it's connected to existing Chrome
                    try:
                        if hasattr(context, '_browser') and context._browser._is_remote:
                            self.logger.info("Not closing remote browser context")
                        else:
                            context.close()
                    except:
                        pass  # Ignore errors when closing
            
            self._print_final_stats()
            return True
            
        except Exception as e:
            self.logger.error(f"Automation failed: {e}")
            print(f"{Fore.RED}❌ Automation failed: {e}{Style.RESET_ALL}")
            return False
        finally:
            self.stats['end_time'] = time.time()
    
    def _get_creators_to_process(self, creator_name: str = None) -> List[Dict[str, Any]]:
        """Get list of creators to process."""
        if creator_name:
            # Find specific creator
            for creator in self.config.creators:
                if creator.get('name') == creator_name:
                    return [creator]
            self.logger.error(f"Creator '{creator_name}' not found in configuration")
            return []
        else:
            # Return all enabled creators
            return self.config.get_enabled_creators()
    
    def _setup_browser(self, playwright):
        """Set up browser connection."""
        self.logger.info("Connecting to existing Chrome session...")
        
        try:
            # Try to connect to existing Chrome debug session first
            browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
            self.logger.info("✅ Connected to existing Chrome session")
            
            # Get existing context or create new one
            contexts = browser.contexts
            if contexts:
                context = contexts[0]
                self.logger.info(f"Using existing browser context with {len(context.pages)} pages")
            else:
                context = browser.new_context(
                    accept_downloads=True,
                    viewport={'width': 1920, 'height': 1080}
                )
                self.logger.info("Created new browser context")
            
            # Set default timeout
            context.set_default_timeout(self.config.browser_timeout)
            
            return context
            
        except Exception as e:
            self.logger.warning(f"Could not connect to existing Chrome session: {e}")
            self.logger.info("Falling back to launching new browser...")
            
            # Fallback to launching new browser
            args = [
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
            
            # Add extension if path provided
            if self.config.extension_path:
                extension_path = os.path.abspath(self.config.extension_path)
                if os.path.exists(extension_path):
                    args.append(f'--load-extension={extension_path}')
                    self.logger.info(f"Loading extension from: {extension_path}")
                else:
                    self.logger.warning(f"Extension path does not exist: {extension_path}")
            
            # Browser context options
            context_options = {
                'accept_downloads': True,
                'viewport': {'width': 1920, 'height': 1080}
            }
            
            browser = playwright.chromium.launch(
                headless=self.config.browser_headless,
                args=args
            )
            context = browser.new_context(**context_options)
            
            # Set default timeout
            context.set_default_timeout(self.config.browser_timeout)
            
            return context
    
    def _process_creator(self, creator: Dict[str, Any], post_extractor: PostExtractor, 
                        export_controller: ExportController, max_posts: int = None, 
                        skip_existing: bool = True):
        """Process a single creator."""
        creator_name = creator.get('name', 'Unknown')
        creator_url = creator.get('url')
        output_subdir = creator.get('output_subfolder', creator_name)
        
        print(f"\n{Fore.CYAN}📂 Processing creator: {creator_name}{Style.RESET_ALL}")
        print(f"URL: {creator_url}")
        print(f"Output: {output_subdir}")
        
        try:
            # Extract post URLs
            print(f"{Fore.YELLOW}🔍 Extracting posts...{Style.RESET_ALL}")
            posts = post_extractor.extract_post_urls(creator_url, max_posts)
            
            if not posts:
                print(f"{Fore.YELLOW}⚠️  No posts found for {creator_name}{Style.RESET_ALL}")
                return
            
            print(f"Found {len(posts)} posts")
            self.stats['total_posts'] += len(posts)
            
            # Process each post
            for i, post in enumerate(posts, 1):
                self._process_post(
                    post, export_controller, output_subdir, 
                    i, len(posts), skip_existing
                )
                
                # Delay between posts
                if i < len(posts):
                    delay = self.config.delay_between_posts
                    if delay > 0:
                        print(f"⏱️  Waiting {delay}s before next post...")
                        time.sleep(delay)
            
            print(f"{Fore.GREEN}✅ Completed creator: {creator_name}{Style.RESET_ALL}")
            
        except Exception as e:
            self.logger.error(f"Error processing creator {creator_name}: {e}")
            print(f"{Fore.RED}❌ Error processing {creator_name}: {e}{Style.RESET_ALL}")
    
    def _process_post(self, post: Dict[str, Any], export_controller: ExportController,
                     output_subdir: str, post_num: int, total_posts: int, 
                     skip_existing: bool = True):
        """Process a single post."""
        post_url = post['url']
        post_title = post.get('title', 'Untitled')
        
        # Truncate title for display
        display_title = post_title[:50] + '...' if len(post_title) > 50 else post_title
        
        print(f"\n[{post_num}/{total_posts}] {display_title}")
        print(f"URL: {post_url}")
        
        try:
            # Check if already exported
            if skip_existing and export_controller.is_post_already_exported(post, output_subdir):
                print(f"{Fore.YELLOW}⏭️  Skipping (already exported){Style.RESET_ALL}")
                self.stats['skipped'] += 1
                return
            
            # Attempt export with retries
            max_retries = self.config.max_retries
            for attempt in range(max_retries):
                if attempt > 0:
                    print(f"🔄 Retry {attempt}/{max_retries}")
                
                try:
                    result = export_controller.export_post(post_url, output_subdir)
                    
                    if result['success']:
                        file_size_mb = result['file_size'] / (1024 * 1024)
                        print(f"{Fore.GREEN}✅ Exported: {result['file_name']} ({file_size_mb:.1f} MB){Style.RESET_ALL}")
                        self.stats['exported'] += 1
                        return
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        if attempt < max_retries - 1:
                            print(f"{Fore.YELLOW}⚠️  Export failed: {error_msg} (retrying...){Style.RESET_ALL}")
                            time.sleep(2)  # Wait before retry
                        else:
                            raise Exception(error_msg)
                            
                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"{Fore.YELLOW}⚠️  Error: {e} (retrying...){Style.RESET_ALL}")
                        time.sleep(2)
                    else:
                        raise
            
            # If we get here, all retries failed
            print(f"{Fore.RED}❌ Export failed after {max_retries} attempts{Style.RESET_ALL}")
            self.stats['failed'] += 1
            
        except Exception as e:
            self.logger.error(f"Error processing post {post_url}: {e}")
            print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")
            self.stats['failed'] += 1
    
    def _print_final_stats(self):
        """Print final automation statistics."""
        if self.stats['end_time'] and self.stats['start_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
        else:
            duration = 0
        
        print(f"\n{Fore.GREEN}🎉 Automation Complete!{Style.RESET_ALL}")
        print("=" * 50)
        print(f"Total posts found: {self.stats['total_posts']}")
        print(f"Successfully exported: {Fore.GREEN}{self.stats['exported']}{Style.RESET_ALL}")
        print(f"Skipped (existing): {Fore.YELLOW}{self.stats['skipped']}{Style.RESET_ALL}")
        print(f"Failed: {Fore.RED}{self.stats['failed']}{Style.RESET_ALL}")
        print(f"Duration: {duration:.1f} seconds")
        
        if self.stats['exported'] > 0:
            avg_time = duration / self.stats['exported']
            print(f"Average time per export: {avg_time:.1f} seconds")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Automate Patreon post exports')
    parser.add_argument('--config', '-c', help='Path to configuration file')
    parser.add_argument('--creator', help='Specific creator to process')
    parser.add_argument('--max-posts', type=int, help='Maximum posts to process per creator')
    parser.add_argument('--no-skip-existing', action='store_true', 
                       help='Re-export posts even if they already exist')
    parser.add_argument('--validate-config', action='store_true',
                       help='Only validate configuration and exit')
    
    args = parser.parse_args()
    
    try:
        # Initialize automation
        automation = PatreonAutomation(args.config)
        
        # Validate configuration only
        if args.validate_config:
            errors = automation.config.validate()
            if errors:
                print(f"{Fore.RED}❌ Configuration errors:{Style.RESET_ALL}")
                for error in errors:
                    print(f"  - {error}")
                return 1
            else:
                print(f"{Fore.GREEN}✅ Configuration is valid{Style.RESET_ALL}")
                return 0
        
        # Run automation
        success = automation.run(
            creator_name=args.creator,
            max_posts=args.max_posts,
            skip_existing=not args.no_skip_existing
        )
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}⏹️  Automation interrupted by user{Style.RESET_ALL}")
        return 130
    except Exception as e:
        print(f"{Fore.RED}❌ Fatal error: {e}{Style.RESET_ALL}")
        return 1


if __name__ == '__main__':
    sys.exit(main())