"""Configuration management for Patreon automation."""

import os
import yaml
from typing import Dict, Any, List
from pathlib import Path


class Config:
    """Configuration manager for the Patreon automation system."""
    
    def __init__(self, config_path: str = None):
        """Initialize configuration.
        
        Args:
            config_path: Path to the configuration file. If None, uses default.
        """
        if config_path is None:
            # Default to config/settings.yaml relative to project root
            project_root = Path(__file__).parent.parent
            config_path = project_root / "config" / "settings.yaml"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config or {}
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML configuration: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value using dot notation.
        
        Args:
            key: Configuration key using dot notation (e.g., 'browser.headless')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
                
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set a configuration value using dot notation.
        
        Args:
            key: Configuration key using dot notation
            value: Value to set
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        config[keys[-1]] = value
    
    def save(self) -> None:
        """Save current configuration back to file."""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, indent=2)
    
    # Convenience properties for common settings
    @property
    def browser_headless(self) -> bool:
        """Whether to run browser in headless mode."""
        return self.get('browser.headless', False)
    
    @property
    def browser_timeout(self) -> int:
        """Browser timeout in milliseconds."""
        return self.get('browser.timeout', 30000)
    
    @property
    def extension_path(self) -> str:
        """Path to the Patreon export extension."""
        return self.get('browser.extension_path', '')
    
    @property
    def user_data_dir(self) -> str:
        """Chrome user data directory."""
        return self.get('browser.user_data_dir', '')
    
    @property
    def output_directory(self) -> str:
        """Base output directory for exports."""
        return self.get('export.output_directory', './downloads')
    
    @property
    def image_quality(self) -> str:
        """Export image quality setting."""
        return self.get('export.image_quality', 'high')
    
    @property
    def include_comments(self) -> bool:
        """Whether to include comments in exports."""
        return self.get('export.include_comments', True)
    
    @property
    def show_download_dialog(self) -> bool:
        """Whether to show download dialog."""
        return self.get('export.show_download_dialog', False)
    
    @property
    def delay_between_posts(self) -> float:
        """Delay between processing posts in seconds."""
        return self.get('automation.delay_between_posts', 3)
    
    @property
    def max_retries(self) -> int:
        """Maximum retries for failed exports."""
        return self.get('automation.max_retries', 3)
    
    @property
    def scroll_delay(self) -> float:
        """Delay for page scrolling in seconds."""
        return self.get('automation.scroll_delay', 1)
    
    @property
    def export_timeout(self) -> float:
        """Timeout for export completion in seconds."""
        return self.get('automation.export_timeout', 120)
    
    @property
    def log_level(self) -> str:
        """Logging level."""
        return self.get('logging.level', 'INFO')
    
    @property
    def log_file(self) -> str:
        """Log file path."""
        return self.get('logging.file', 'logs/automation.log')
    
    @property
    def creators(self) -> List[Dict[str, Any]]:
        """List of creators to process."""
        return self.get('creators', [])
    
    def get_enabled_creators(self) -> List[Dict[str, Any]]:
        """Get list of enabled creators."""
        return [creator for creator in self.creators if creator.get('enabled', True)]
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Check extension path if provided (optional for pre-installed extensions)
        if self.extension_path and not os.path.exists(self.extension_path):
            errors.append(f"Extension path does not exist: {self.extension_path}")
        
        # Validate image quality
        valid_qualities = ['high', 'medium', 'low']
        if self.image_quality not in valid_qualities:
            errors.append(f"export.image_quality must be one of: {valid_qualities}")
        
        # Validate log level
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
        if self.log_level not in valid_levels:
            errors.append(f"logging.level must be one of: {valid_levels}")
        
        # Check creators configuration
        if not self.creators:
            errors.append("No creators configured")
        else:
            for i, creator in enumerate(self.creators):
                if 'url' not in creator:
                    errors.append(f"Creator {i}: missing 'url' field")
                if 'name' not in creator:
                    errors.append(f"Creator {i}: missing 'name' field")
        
        return errors