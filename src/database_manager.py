"""
Database manager for handling multi-creator discovered and downloaded posts.
"""

import json
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
import dateutil.parser


class DatabaseManager:
    """Manages discovered and downloaded posts databases for multiple creators."""
    
    def __init__(self, base_dir: str = "database"):
        self.base_dir = Path(base_dir)
        self.discovered_dir = self.base_dir / "discovered"
        self.downloaded_dir = self.base_dir / "downloaded"
        
        # Create directories if they don't exist
        self.discovered_dir.mkdir(parents=True, exist_ok=True)
        self.downloaded_dir.mkdir(parents=True, exist_ok=True)
    
    def _extract_creator_name(self, creator_url: str) -> str:
        """Extract creator name from Patreon URL."""
        # Extract from patterns like:
        # https://www.patreon.com/c/trendinvestorhk/posts
        # https://www.patreon.com/trendinvestorhk/posts
        match = re.search(r'patreon\.com/(?:c/)?([^/]+)', creator_url)
        if match:
            return match.group(1)
        
        # Fallback: use hash of URL
        return f"creator_{hash(creator_url) % 100000}"
    
    def get_discovered_db_path(self, creator_url: str) -> Path:
        """Get path to discovered posts database for a creator."""
        creator_name = self._extract_creator_name(creator_url)
        return self.discovered_dir / f"{creator_name}.json"
    
    def get_downloaded_db_path(self, creator_url: str) -> Path:
        """Get path to downloaded posts database for a creator."""
        creator_name = self._extract_creator_name(creator_url)
        return self.downloaded_dir / f"{creator_name}.json"
    
    def load_discovered_posts(self, creator_url: str) -> Optional[Dict[str, Any]]:
        """Load discovered posts database for a creator."""
        db_path = self.get_discovered_db_path(creator_url)
        if not db_path.exists():
            return None
        
        try:
            with open(db_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️  Error loading discovered posts database: {e}")
            return None
    
    def save_discovered_posts(self, creator_url: str, posts_data: Dict[str, Any]) -> bool:
        """Save discovered posts database for a creator."""
        db_path = self.get_discovered_db_path(creator_url)
        
        try:
            with open(db_path, 'w', encoding='utf-8') as f:
                json.dump(posts_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"❌ Error saving discovered posts database: {e}")
            return False
    
    def load_downloaded_posts(self, creator_url: str) -> Dict[str, Any]:
        """Load downloaded posts database for a creator."""
        db_path = self.get_downloaded_db_path(creator_url)
        creator_name = self._extract_creator_name(creator_url)
        
        if not db_path.exists():
            return {
                "creator": creator_name,
                "creator_url": creator_url,
                "tracking_started_at": datetime.now().isoformat(),
                "total_downloaded": 0,
                "downloaded_posts": {}
            }
        
        try:
            with open(db_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️  Error loading downloaded posts database: {e}")
            return {
                "creator": creator_name,
                "creator_url": creator_url,
                "tracking_started_at": datetime.now().isoformat(),
                "total_downloaded": 0,
                "downloaded_posts": {}
            }
    
    def save_downloaded_posts(self, creator_url: str, downloaded_data: Dict[str, Any]) -> bool:
        """Save downloaded posts database for a creator."""
        db_path = self.get_downloaded_db_path(creator_url)
        
        try:
            with open(db_path, 'w', encoding='utf-8') as f:
                json.dump(downloaded_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"❌ Error saving downloaded posts database: {e}")
            return False
    
    def check_needs_discovery(self, creator_url: str, force_full: bool = False) -> bool:
        """Check if discovery is needed for a creator."""
        if force_full:
            return True
        
        existing_db = self.load_discovered_posts(creator_url)
        if not existing_db:
            return True
        
        # Check if database is recent (less than 24 hours old)
        try:
            discovered_at = dateutil.parser.parse(existing_db.get('discovered_at'))
            age_hours = (datetime.now() - discovered_at.replace(tzinfo=None)).total_seconds() / 3600
            if age_hours > 24:
                print(f"📅 Database is {age_hours:.1f} hours old, needs refresh")
                return True
        except Exception:
            return True
        
        return False
    
    def get_latest_post_info(self, creator_url: str) -> Optional[Dict[str, Any]]:
        """Get the latest post information from discovered database."""
        db = self.load_discovered_posts(creator_url)
        if not db or not db.get('posts'):
            return None
        
        posts = db['posts']
        # Find the post with the most recent date
        latest_post = None
        latest_date = None
        
        for post in posts:
            if not post.get('publish_date'):
                continue
            
            try:
                post_date = dateutil.parser.parse(post['publish_date'])
                if latest_date is None or post_date > latest_date:
                    latest_date = post_date
                    latest_post = post
            except Exception:
                continue
        
        return latest_post
    
    def merge_new_posts(self, creator_url: str, new_posts: List[Dict[str, Any]], 
                       existing_db: Dict[str, Any]) -> Dict[str, Any]:
        """Merge new posts with existing database, avoiding duplicates."""
        existing_urls = {post['url'] for post in existing_db.get('posts', [])}
        
        # Filter out posts that already exist
        truly_new_posts = [post for post in new_posts if post['url'] not in existing_urls]
        
        if not truly_new_posts:
            print(f"✅ No new posts found, existing database is up to date")
            return existing_db
        
        # Merge posts
        merged_posts = existing_db.get('posts', []) + truly_new_posts
        
        # Update database metadata
        posts_with_dates = [p for p in merged_posts if p.get('publish_date')]
        
        merged_db = {
            "creator_url": creator_url,
            "total_posts": len(merged_posts),
            "posts_with_dates": len(posts_with_dates),
            "date_extraction_success_rate": len(posts_with_dates)/len(merged_posts)*100 if merged_posts else 0,
            "discovered_at": datetime.now().isoformat(),
            "discovery_method": "incremental_update",
            "last_update": datetime.now().isoformat(),
            "new_posts_added": len(truly_new_posts),
            "posts": merged_posts
        }
        
        print(f"🔄 Merged {len(truly_new_posts)} new posts with {len(existing_db.get('posts', []))} existing posts")
        
        return merged_db
    
    def list_creators(self) -> List[Dict[str, Any]]:
        """List all creators with their database information."""
        creators = []
        
        for db_file in self.discovered_dir.glob("*.json"):
            try:
                with open(db_file, 'r', encoding='utf-8') as f:
                    db = json.load(f)
                
                creator_name = db_file.stem
                downloaded_db = self.load_downloaded_posts(db.get('creator_url', ''))
                
                creators.append({
                    "name": creator_name,
                    "url": db.get('creator_url', ''),
                    "total_posts": db.get('total_posts', 0),
                    "downloaded_posts": downloaded_db.get('total_downloaded', 0),
                    "discovered_at": db.get('discovered_at', ''),
                    "last_update": db.get('last_update', db.get('discovered_at', ''))
                })
            except Exception as e:
                print(f"⚠️  Error reading database {db_file}: {e}")
        
        return creators