"""Post URL extraction from Patreon creator pages."""

import re
import time
import logging
from typing import List, Set, Dict, Any
from urllib.parse import urljoin, urlparse
from playwright.sync_api import Page, TimeoutError as PlaywrightTimeoutError
from datetime import datetime, timedelta
import dateutil.parser


class PostExtractor:
    """Extracts post URLs from Patreon creator pages."""
    
    def __init__(self, page: Page, logger: logging.Logger = None):
        """Initialize the post extractor.
        
        Args:
            page: Playwright page instance
            logger: Logger instance
        """
        self.page = page
        self.logger = logger or logging.getLogger(__name__)
        
    def extract_post_urls(self, creator_url: str, max_posts: int = None) -> List[Dict[str, Any]]:
        """Extract all post URLs from a creator's posts page.
        
        Args:
            creator_url: URL to the creator's posts page
            max_posts: Maximum number of posts to extract (None for all)
            
        Returns:
            List of dictionaries containing post information
        """
        self.logger.info(f"Extracting posts from: {creator_url}")
        
        try:
            # Navigate to the creator's posts page
            self.page.goto(creator_url, wait_until='networkidle')
            self.page.wait_for_timeout(3000)  # Wait for dynamic content
            
            posts = []
            seen_urls = set()
            scroll_attempts = 0
            max_scroll_attempts = 50
            last_post_count = 0
            no_change_count = 0
            
            while scroll_attempts < max_scroll_attempts:
                # Extract current posts
                current_posts = self._extract_current_posts()
                
                # Add new posts to our collection
                for post in current_posts:
                    if post['url'] not in seen_urls:
                        posts.append(post)
                        seen_urls.add(post['url'])
                        self.logger.debug(f"Found post: {post['title']} - {post['url']}")
                
                self.logger.info(f"Currently have {len(posts)} posts")
                
                # Check if we've reached the maximum
                if max_posts and len(posts) >= max_posts:
                    posts = posts[:max_posts]
                    break
                
                # Try to click "Load more posts" button first (this loads more posts, not comments)
                load_more_clicked = self._click_load_more_posts_if_present()
                if load_more_clicked:
                    self.logger.info("Clicked 'Load more posts' button, waiting for new posts...")
                    self.page.wait_for_timeout(5000)  # Wait longer for posts to load
                    no_change_count = 0  # Reset counter since we actively loaded more
                    scroll_attempts = 0  # Reset scroll attempts
                    continue
                
                # Check if we found new posts in this iteration
                if len(posts) == last_post_count:
                    no_change_count += 1
                    if no_change_count >= 3:  # No new posts found after 3 attempts
                        self.logger.info("No new posts found after multiple attempts, stopping")
                        break
                else:
                    no_change_count = 0
                    last_post_count = len(posts)
                
                # Scroll down to reveal more content
                self.logger.debug(f"Scrolling to reveal more content (attempt {scroll_attempts + 1})")
                self._scroll_and_wait()
                scroll_attempts += 1
            
            self.logger.info(f"Extracted {len(posts)} posts from {creator_url}")
            return posts
            
        except Exception as e:
            self.logger.error(f"Error extracting posts from {creator_url}: {e}")
            raise
    
    def _extract_current_posts(self) -> List[Dict[str, Any]]:
        """Extract post information from currently loaded page content."""
        posts = []
        
        try:
            # Look for post elements - Patreon uses various selectors
            post_selectors = [
                'a[href*="/posts/"]',  # Links containing /posts/
                '[data-tag="post-card"] a',  # Post card links
                'article a[href*="/posts/"]',  # Article links
            ]
            
            for selector in post_selectors:
                try:
                    post_elements = self.page.query_selector_all(selector)
                    self.logger.debug(f"Found {len(post_elements)} elements with selector: {selector}")
                    
                    for element in post_elements:
                        try:
                            href = element.get_attribute('href')
                            if not href or '/posts/' not in href:
                                continue
                            
                            # Make URL absolute
                            if href.startswith('/'):
                                url = f"https://www.patreon.com{href}"
                            else:
                                url = href
                            
                            # Extract post ID from URL - be more flexible with the pattern
                            post_id_match = re.search(r'/posts/[^/]*-(\d+)', url)
                            if not post_id_match:
                                # Try alternative pattern for posts with just numbers
                                post_id_match = re.search(r'/posts/(\d+)', url)
                            
                            if not post_id_match:
                                self.logger.debug(f"Could not extract post ID from URL: {url}")
                                continue
                            
                            post_id = post_id_match.group(1)
                            
                            # Try to extract title
                            title = self._extract_post_title(element)
                            
                            # Try to extract publish date
                            publish_date = self._extract_post_date(element)
                            
                            post_data = {
                                'url': url,
                                'id': post_id,
                                'title': title,
                                'publish_date': publish_date,
                                'extracted_at': time.time()
                            }
                            
                            posts.append(post_data)
                            self.logger.debug(f"Extracted post: {title[:50]} - {url}")
                            
                        except Exception as e:
                            self.logger.debug(f"Error processing post element: {e}")
                            continue
                    
                    # If we found posts with this selector, we can break
                    if posts:
                        self.logger.debug(f"Successfully extracted {len(posts)} posts with selector: {selector}")
                        break
                        
                except Exception as e:
                    self.logger.debug(f"Error with selector {selector}: {e}")
                    continue
        
        except Exception as e:
            self.logger.error(f"Error extracting current posts: {e}")
        
        # Remove duplicates based on URL
        seen_urls = set()
        unique_posts = []
        for post in posts:
            if post['url'] not in seen_urls:
                unique_posts.append(post)
                seen_urls.add(post['url'])
        
        return unique_posts
    
    def _extract_post_title(self, element) -> str:
        """Extract post title from a post element."""
        try:
            # Try different methods to find the title
            title_selectors = [
                'span[data-tag="post-title"]',
                'h1', 'h2', 'h3',
                '[class*="title"]',
                'span', 'div'
            ]
            
            # First check the element itself
            title_text = element.text_content()
            if title_text and len(title_text.strip()) > 0:
                return title_text.strip()[:100]  # Limit title length
            
            # Then check parent containers
            parent = element
            for _ in range(3):  # Check up to 3 levels up
                parent = parent.query_selector('..')
                if not parent:
                    break
                
                for selector in title_selectors:
                    title_element = parent.query_selector(selector)
                    if title_element:
                        title_text = title_element.text_content()
                        if title_text and len(title_text.strip()) > 0:
                            return title_text.strip()[:100]
            
            # Fallback: extract from URL or use generic title
            href = element.get_attribute('href') or ''
            if '/posts/' in href:
                post_id = re.search(r'/posts/(\d+)', href)
                if post_id:
                    return f"Post {post_id.group(1)}"
            
            return "Untitled Post"
            
        except Exception as e:
            self.logger.debug(f"Error extracting title: {e}")
            return "Untitled Post"
    
    def _parse_human_date(self, date_text: str) -> str:
        """Convert human-readable date to ISO format."""
        try:
            date_text = date_text.strip().lower()
            now = datetime.now()
            
            # Handle relative dates
            if "ago" in date_text:
                # Extract number and unit
                if "second" in date_text:
                    match = re.search(r'(\d+)\s*second', date_text)
                    if match:
                        seconds = int(match.group(1))
                        result_date = now - timedelta(seconds=seconds)
                        return result_date.isoformat()
                
                elif "minute" in date_text:
                    match = re.search(r'(\d+)\s*minute', date_text)
                    if match:
                        minutes = int(match.group(1))
                        result_date = now - timedelta(minutes=minutes)
                        return result_date.isoformat()
                
                elif "hour" in date_text:
                    match = re.search(r'(\d+)\s*hour', date_text)
                    if match:
                        hours = int(match.group(1))
                        result_date = now - timedelta(hours=hours)
                        return result_date.isoformat()
                
                elif "day" in date_text:
                    match = re.search(r'(\d+)\s*day', date_text)
                    if match:
                        days = int(match.group(1))
                        result_date = now - timedelta(days=days)
                        return result_date.isoformat()
                
                elif "week" in date_text:
                    match = re.search(r'(\d+)\s*week', date_text)
                    if match:
                        weeks = int(match.group(1))
                        result_date = now - timedelta(weeks=weeks)
                        return result_date.isoformat()
                
                elif "month" in date_text:
                    match = re.search(r'(\d+)\s*month', date_text)
                    if match:
                        months = int(match.group(1))
                        result_date = now - timedelta(days=months * 30)  # Approximate
                        return result_date.isoformat()
                
                elif "year" in date_text:
                    match = re.search(r'(\d+)\s*year', date_text)
                    if match:
                        years = int(match.group(1))
                        result_date = now - timedelta(days=years * 365)  # Approximate
                        return result_date.isoformat()
            
            # Handle special cases
            elif date_text == "yesterday":
                result_date = now - timedelta(days=1)
                return result_date.isoformat()
            
            elif date_text == "today":
                return now.date().isoformat()
            
            elif date_text in ["just now", "now"]:
                return now.isoformat()
            
            # Try to parse absolute dates like "Jul 8, 2024", "Nov 21, 2024"
            else:
                try:
                    # Use dateutil.parser for flexible parsing
                    parsed_date = dateutil.parser.parse(date_text)
                    return parsed_date.isoformat()
                except:
                    # If dateutil fails, try some common patterns
                    patterns = [
                        r'(\w{3}) (\d{1,2}), (\d{4})',  # "Jul 8, 2024"
                        r'(\w{3,9}) (\d{1,2}), (\d{4})',  # "November 8, 2024"
                        r'(\d{1,2})/(\d{1,2})/(\d{4})',  # "7/8/2024"
                        r'(\d{4})-(\d{1,2})-(\d{1,2})',  # "2024-07-08"
                    ]
                    
                    for pattern in patterns:
                        match = re.search(pattern, date_text)
                        if match:
                            try:
                                parsed_date = dateutil.parser.parse(date_text)
                                return parsed_date.isoformat()
                            except:
                                continue
            
            # If all parsing fails, return the original text
            self.logger.debug(f"Could not parse date: '{date_text}'")
            return date_text
            
        except Exception as e:
            self.logger.debug(f"Error parsing date '{date_text}': {e}")
            return date_text
    
    def _extract_post_date(self, element) -> str:
        """Extract post publish date from a post element."""
        try:
            # Look for Patreon-specific date selectors first
            patreon_date_selectors = [
                '[data-tag="post-published-at"]',
                '[data-tag="post-published-at"] span span',
                'a[data-tag="post-published-at"]',
                'a[data-tag="post-published-at"] span'
            ]
            
            # Generic date selectors as fallback
            generic_date_selectors = [
                'time',
                '[datetime]',
                '[class*="date"]',
                '[class*="time"]'
            ]
            
            all_selectors = patreon_date_selectors + generic_date_selectors
            
            # Check the element and its parent containers (going up the DOM tree)
            containers = [element]
            parent = element
            for _ in range(5):  # Look up to 5 levels up
                parent = parent.query_selector('..')
                if parent:
                    containers.append(parent)
            
            for container in containers:
                for selector in all_selectors:
                    date_element = container.query_selector(selector)
                    if date_element:
                        # Try datetime attribute first
                        datetime_attr = date_element.get_attribute('datetime')
                        if datetime_attr:
                            return datetime_attr
                        
                        # Then try text content
                        date_text = date_element.text_content()
                        if date_text and len(date_text.strip()) > 0:
                            # Clean up the date text (remove extra whitespace)
                            cleaned_date = date_text.strip()
                            # Skip if it's just punctuation or very short
                            if len(cleaned_date) > 3 and cleaned_date not in ['·', '-', '|']:
                                # Parse human-readable date to ISO format
                                parsed_date = self._parse_human_date(cleaned_date)
                                return parsed_date
            
            # Final fallback: look for any element with date-like text patterns
            try:
                # Look for post card containers
                post_card = element.query_selector('..').query_selector('[data-tag="post-card"]')
                if not post_card:
                    # Try going up more levels
                    current = element
                    for _ in range(10):
                        current = current.query_selector('..')
                        if not current:
                            break
                        post_card = current.query_selector('[data-tag="post-card"]')
                        if post_card:
                            break
                
                if post_card:
                    # Look for date patterns within the post card
                    date_pattern_element = post_card.query_selector('[data-tag="post-published-at"]')
                    if date_pattern_element:
                        date_text = date_pattern_element.text_content()
                        if date_text and len(date_text.strip()) > 3:
                            cleaned_date = date_text.strip()
                            parsed_date = self._parse_human_date(cleaned_date)
                            return parsed_date
            except:
                pass
            
            return ""
            
        except Exception as e:
            self.logger.debug(f"Error extracting date: {e}")
            return ""
    
    def _scroll_and_wait(self):
        """Scroll down and wait for new content to load."""
        try:
            # Scroll to bottom
            self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            
            # Wait for potential loading
            self.page.wait_for_timeout(2000)
            
            # Try to wait for network idle (new content loading)
            try:
                self.page.wait_for_load_state('networkidle', timeout=5000)
            except PlaywrightTimeoutError:
                pass  # Timeout is expected, continue
                
        except Exception as e:
            self.logger.debug(f"Error during scroll: {e}")
    
    def _click_load_more_posts_if_present(self):
        """Click 'Load more' button specifically for posts (not comments)."""
        try:
            self.logger.debug("Looking for 'Load more posts' button...")
            
            # Scroll to bottom first to make the button visible
            self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            self.page.wait_for_timeout(1000)
            
            # Strategy 1: Look for buttons that say exactly "Load more" (not "Load more comments")
            load_more_buttons = self.page.query_selector_all('button:has-text("Load more")')
            
            for button in load_more_buttons:
                try:
                    if button.is_visible() and not button.is_disabled():
                        button_text = (button.text_content() or "").strip()
                        
                        # Only click if it's exactly "Load more" (not "Load more comments")
                        if button_text == "Load more":
                            self.logger.info(f"Found 'Load more posts' button: '{button_text}'")
                            button.click()
                            return True
                            
                except Exception as e:
                    self.logger.debug(f"Error checking button: {e}")
                    continue
            
            # Strategy 2: Look for Show more buttons that might load posts
            show_more_buttons = self.page.query_selector_all('button:has-text("Show more")')
            
            for button in show_more_buttons:
                try:
                    if button.is_visible() and not button.is_disabled():
                        button_text = (button.text_content() or "").strip()
                        
                        # Check if this Show more button is for posts (not comments)
                        # Usually the posts "Show more" is at the bottom of the page
                        button_rect = button.bounding_box()
                        if button_rect:
                            page_height = self.page.evaluate("document.body.scrollHeight")
                            # If button is in the bottom 20% of the page, it's likely for posts
                            if button_rect['y'] > page_height * 0.8:
                                self.logger.info(f"Found potential 'Show more posts' button at bottom: '{button_text}'")
                                button.click()
                                return True
                                
                except Exception as e:
                    self.logger.debug(f"Error checking Show more button: {e}")
                    continue
            
            # Strategy 3: Look for any button near the bottom that might load more posts
            all_buttons = self.page.query_selector_all('button')
            page_height = self.page.evaluate("document.body.scrollHeight")
            
            for button in all_buttons:
                try:
                    if button.is_visible() and not button.is_disabled():
                        button_text = (button.text_content() or "").lower().strip()
                        button_rect = button.bounding_box()
                        
                        # Look for buttons in bottom 10% of page with relevant text
                        if (button_rect and button_rect['y'] > page_height * 0.9 and
                            any(keyword in button_text for keyword in ['more', 'load', 'next', '更多', '加载'])):
                            
                            # Exclude comment-related buttons
                            if 'comment' not in button_text:
                                self.logger.info(f"Found potential pagination button: '{button_text}'")
                                button.click()
                                return True
                                
                except Exception as e:
                    continue
            
            self.logger.debug("No 'Load more posts' button found")
            return False
            
        except Exception as e:
            self.logger.debug(f"Error looking for Load more posts button: {e}")
            return False
    
    def _click_load_more_if_present(self):
        """Click 'Load more' or 'Show more' button if present."""
        try:
            load_more_selectors = [
                'button:has-text("Load more")',
                'button:has-text("Show more")',
                'button:has-text("View more")',
                'button:has-text("加載更多")',  # Chinese "Load more"
                'button:has-text("显示更多")',  # Chinese "Show more" 
                '[data-tag="load-more"]',
                'button[class*="load"]',
                'button[class*="more"]',
                'button[class*="Load"]',
                'button[class*="More"]'
            ]
            
            for selector in load_more_selectors:
                try:
                    button = self.page.query_selector(selector)
                    if button and button.is_visible() and not button.is_disabled():
                        button_text = button.text_content() or ""
                        self.logger.debug(f"Found potential load more button: '{button_text}' with selector: {selector}")
                        button.click()
                        self.logger.debug(f"Clicked load more button: {selector}")
                        return True
                except Exception as e:
                    self.logger.debug(f"Error clicking {selector}: {e}")
                    continue
            
            # Also try looking for any button with "load" or "more" text
            all_buttons = self.page.query_selector_all('button')
            for button in all_buttons:
                try:
                    if button.is_visible() and not button.is_disabled():
                        button_text = (button.text_content() or "").lower()
                        if any(keyword in button_text for keyword in ['load', 'more', '更多', '加载']):
                            self.logger.debug(f"Found text-based load more button: '{button_text}'")
                            button.click()
                            return True
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.debug(f"Error checking for load more button: {e}")
            return False
    
    def validate_post_url(self, url: str) -> bool:
        """Validate that a URL is a valid Patreon post URL.
        
        Args:
            url: URL to validate
            
        Returns:
            True if valid post URL
        """
        try:
            parsed = urlparse(url)
            return (
                parsed.netloc == 'www.patreon.com' and
                '/posts/' in parsed.path and
                re.search(r'/posts/\d+', parsed.path) is not None
            )
        except Exception:
            return False