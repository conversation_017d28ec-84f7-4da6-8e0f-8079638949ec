#!/usr/bin/env python3
"""
Check the status of posts database and export progress.
This is the main script to monitor your automation.
"""

import json
import sys
from pathlib import Path
from datetime import datetime

def show_posts_database():
    """Show posts database status."""
    
    print("📊 Posts Database")
    print("-" * 20)
    
    db_file = Path("posts_database.json")
    if not db_file.exists():
        print("❌ No posts database found")
        print("   Run: uv run python discover_posts.py")
        return None
    
    with open(db_file, 'r', encoding='utf-8') as f:
        database = json.load(f)
    
    print(f"✅ Database: {db_file.name} ({db_file.stat().st_size / 1024:.1f} KB)")
    print(f"📅 Created: {database['discovered_at'][:16].replace('T', ' ')}")
    print(f"📝 Total posts: {database['total_posts']}")
    print(f"📅 Date success: {database.get('date_extraction_success_rate', 0):.1f}%")
    print(f"🔗 Creator: {database['creator_url']}")
    
    return database

def show_download_progress():
    """Show download progress."""
    
    print(f"\n📦 Download Progress")
    print("-" * 20)
    
    tracking_file = Path("downloaded_posts.json")
    if not tracking_file.exists():
        print("❌ No download tracking found")
        print("   Downloads will be tracked when you run export_posts.py")
        return None
    
    with open(tracking_file, 'r', encoding='utf-8') as f:
        downloaded = json.load(f)
    
    print(f"✅ Downloaded: {downloaded['total_downloaded']} posts")
    if downloaded['total_downloaded'] > 0:
        print(f"📅 Last download: {downloaded['last_updated'][:16].replace('T', ' ')}")
        
        # Show recent downloads
        recent = sorted(
            downloaded['downloaded_posts'].items(),
            key=lambda x: x[1]['downloaded_at'],
            reverse=True
        )[:3]
        
        print(f"🕐 Recent downloads:")
        for i, (url, info) in enumerate(recent, 1):
            size_mb = info['file_size_bytes'] / (1024 * 1024)
            title = info['title'][:40] + "..." if len(info['title']) > 40 else info['title']
            print(f"   {i}. {title} ({size_mb:.1f} MB)")
    
    return downloaded

def show_files_on_disk():
    """Show actual PDF files on disk."""
    
    print(f"\n📁 Files on Disk")
    print("-" * 15)
    
    downloads_dir = Path("downloads/trendinvestor")
    if not downloads_dir.exists():
        print(f"❌ No downloads directory: {downloads_dir}")
        return []
    
    pdf_files = list(downloads_dir.glob("*.pdf"))
    if not pdf_files:
        print(f"❌ No PDF files found")
        return []
    
    pdf_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
    total_size = sum(f.stat().st_size for f in pdf_files)
    
    print(f"✅ Found: {len(pdf_files)} PDF files ({total_size / (1024*1024):.1f} MB)")
    print(f"📂 Location: {downloads_dir}")
    
    return pdf_files

def show_progress_summary(database, downloaded):
    """Show overall progress."""
    
    print(f"\n🎯 Progress Summary")
    print("-" * 20)
    
    if not database:
        print("❌ No database - run discover_posts.py first")
        return
    
    total_posts = database['total_posts']
    downloaded_count = downloaded['total_downloaded'] if downloaded else 0
    remaining = total_posts - downloaded_count
    progress_pct = (downloaded_count / total_posts) * 100 if total_posts > 0 else 0
    
    print(f"📊 Progress: {downloaded_count}/{total_posts} ({progress_pct:.1f}%)")
    print(f"✅ Downloaded: {downloaded_count} posts")
    print(f"⏳ Remaining: {remaining} posts")
    
    if remaining > 0:
        print(f"\n🚀 Next steps:")
        print(f"   • Export all: uv run python export_posts.py")
        print(f"   • Export 10: uv run python export_posts.py 10")
    else:
        print(f"\n🎉 All posts downloaded!")

def show_usage_guide():
    """Show simple usage guide."""
    
    print(f"\n💡 Usage Guide")
    print("-" * 15)
    print(f"1. 🔍 Discover posts:")
    print(f"   uv run python discover_posts.py          # All posts (default)")
    print(f"   uv run python discover_posts.py --current # Current posts only")
    print(f"")
    print(f"2. 📦 Export posts:")
    print(f"   uv run python export_posts.py")
    print(f"   uv run python export_posts.py 10        # Limit to 10 posts")
    print(f"")
    print(f"3. 📊 Check status:")
    print(f"   uv run python check_status.py")

def main():
    """Main function."""
    
    print("🔍 Patreon Automation Status")
    print("=" * 40)
    
    # Show status
    database = show_posts_database()
    downloaded = show_download_progress()
    files = show_files_on_disk()
    
    # Show progress
    show_progress_summary(database, downloaded)
    
    # Show usage guide
    show_usage_guide()

if __name__ == "__main__":
    main()