#!/usr/bin/env python3
"""Quick progress check script."""

import time
from pathlib import Path
import requests

def check_progress():
    """Check current progress of the automation."""
    print("📊 Checking Automation Progress")
    print("=" * 40)
    
    # Check downloads
    downloads_dir = Path("downloads/trendinvestor")
    if downloads_dir.exists():
        pdf_files = list(downloads_dir.glob("*.pdf"))
        print(f"📁 PDFs exported: {len(pdf_files)}")
        
        total_size = sum(f.stat().st_size for f in pdf_files)
        print(f"💾 Total size: {total_size / (1024 * 1024):.1f} MB")
        
        if pdf_files:
            print(f"\n📋 Recent files:")
            # Sort by modification time
            recent_files = sorted(pdf_files, key=lambda f: f.stat().st_mtime, reverse=True)
            for f in recent_files[:5]:
                size_mb = f.stat().st_size / (1024 * 1024)
                mtime = time.ctime(f.stat().st_mtime)
                print(f"   • {f.name} ({size_mb:.1f} MB) - {mtime}")
    else:
        print("📁 No downloads folder found")
    
    # Check Chrome tabs
    try:
        response = requests.get("http://localhost:9222/json", timeout=2)
        if response.status_code == 200:
            tabs = response.json()
            patreon_tabs = [tab for tab in tabs if 'patreon.com' in tab.get('url', '')]
            
            print(f"\n🌐 Chrome status: {len(patreon_tabs)} Patreon tab(s) active")
            for tab in patreon_tabs:
                title = tab.get('title', 'Unknown')[:50]
                url = tab.get('url', 'Unknown')
                print(f"   • {title}")
                if '/posts/' in url:
                    print(f"     🎯 Currently on individual post")
                elif '/posts' in url:
                    print(f"     📋 On posts listing page")
        else:
            print("🌐 Chrome debug not responding")
            
    except Exception:
        print("🌐 Chrome debug not available")
    
    # Check Downloads folder for very recent files
    downloads_folder = Path.home() / "Downloads"
    if downloads_folder.exists():
        recent_pdfs = []
        cutoff_time = time.time() - 3600  # Last hour
        
        for pdf in downloads_folder.glob("*.pdf"):
            if pdf.stat().st_mtime > cutoff_time:
                recent_pdfs.append(pdf)
        
        if recent_pdfs:
            print(f"\n📥 Recent downloads (last hour): {len(recent_pdfs)}")
            recent_pdfs.sort(key=lambda f: f.stat().st_mtime, reverse=True)
            for f in recent_pdfs[:3]:
                size_mb = f.stat().st_size / (1024 * 1024)
                mtime = time.ctime(f.stat().st_mtime)
                print(f"   • {f.name} ({size_mb:.1f} MB) - {mtime}")

if __name__ == '__main__':
    check_progress()