#!/usr/bin/env python3
"""Debug script to inspect post extraction."""

import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def debug_post_extraction():
    """Debug post extraction by inspecting the page."""
    print("🔍 Debugging post extraction...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # Connect to existing Chrome
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            context = browser.contexts[0]
            
            # Use existing page or create new one
            pages = context.pages
            if pages:
                page = pages[0]
            else:
                page = context.new_page()
            
            creator_url = "https://www.patreon.com/c/trendinvestorhk/posts"
            print(f"Navigating to: {creator_url}")
            page.goto(creator_url, timeout=30000)
            page.wait_for_timeout(3000)
            
            print(f"Page title: {page.title()}")
            print(f"Current URL: {page.url}")
            
            # Try different selectors to find posts
            selectors_to_try = [
                'a[href*="/posts/"]',
                '[data-tag="post-card"] a',
                'article a[href*="/posts/"]',
                'a[href*="posts"]',
                '[data-tag="post-card"]',
                'article',
                'h1, h2, h3',
                'div[class*="post"]',
                'div[class*="card"]',
                '[data-testid*="post"]'
            ]
            
            for selector in selectors_to_try:
                elements = page.query_selector_all(selector)
                print(f"Selector '{selector}': found {len(elements)} elements")
                
                if elements and len(elements) > 0:
                    # Show first few elements
                    for i, element in enumerate(elements[:3]):
                        try:
                            href = element.get_attribute('href')
                            text = element.text_content()[:100] if element.text_content() else "No text"
                            print(f"  {i+1}. href={href}, text={text}")
                        except Exception as e:
                            print(f"  {i+1}. Error getting info: {e}")
            
            # Get page content for manual inspection
            print("\n📄 Page structure (first 2000 chars):")
            content = page.content()
            print(content[:2000])
            
            # Look for any text that might indicate posts
            print("\n🔎 Looking for post-related text patterns...")
            post_patterns = ['post', 'Post', 'POST', 'href="/posts/', 'patreon.com/posts']
            for pattern in post_patterns:
                if pattern in content:
                    print(f"  ✅ Found pattern: {pattern}")
                else:
                    print(f"  ❌ Pattern not found: {pattern}")
            
            print("\nWaiting 10 seconds for manual inspection...")
            time.sleep(10)
            
            return True
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        return False

def main():
    """Run debug."""
    success = debug_post_extraction()
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())