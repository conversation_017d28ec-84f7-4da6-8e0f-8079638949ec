#!/usr/bin/env python3
"""Run full export with better progress monitoring."""

import sys
import time
import signal
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully."""
    print('\n⏹️  Export interrupted by user. Progress has been saved.')
    print('You can resume later by running the same command.')
    sys.exit(0)

def run_full_export():
    """Run the full export with progress monitoring."""
    try:
        from patreon_automation import PatreonAutomation
        from colorama import init, Fore, Style
        
        # Initialize colorama
        init(autoreset=True)
        
        # Set up signal handler for graceful interruption
        signal.signal(signal.SIGINT, signal_handler)
        
        print(f"{Fore.GREEN}🚀 Starting FULL Export of trendinvestorhk{Style.RESET_ALL}")
        print("=" * 60)
        print("📋 This will:")
        print("   • Load ALL posts using pagination")
        print("   • Export each post as a high-quality PDF")
        print("   • Show real-time progress")
        print("   • Save files to downloads/trendinvestor/")
        print("   • Skip already exported posts")
        print("")
        print(f"{Fore.YELLOW}⚠️  This may take several hours for all posts!{Style.RESET_ALL}")
        print(f"💡 You can press Ctrl+C to stop and resume later")
        print("")
        
        # Initialize automation
        automation = PatreonAutomation()
        
        # Check initial state
        downloads_dir = Path("downloads/trendinvestor")
        initial_files = list(downloads_dir.glob("*.pdf")) if downloads_dir.exists() else []
        print(f"📁 Currently have {len(initial_files)} PDFs:")
        for f in initial_files:
            size_mb = f.stat().st_size / (1024 * 1024)
            print(f"   - {f.name} ({size_mb:.1f} MB)")
        
        print(f"\n{Fore.CYAN}🎯 Starting automation...{Style.RESET_ALL}")
        print("-" * 60)
        
        # Run the automation
        success = automation.run(
            creator_name="trendinvestorhk",
            max_posts=None,  # No limit - get ALL posts
            skip_existing=True
        )
        
        if success:
            print(f"\n{Fore.GREEN}🎉 Export completed successfully!{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.RED}❌ Export completed with some errors{Style.RESET_ALL}")
        
        # Show final results
        final_files = list(downloads_dir.glob("*.pdf")) if downloads_dir.exists() else []
        new_files = [f for f in final_files if f not in initial_files]
        
        print(f"\n📊 Final Results:")
        print(f"   New PDFs exported: {len(new_files)}")
        print(f"   Total PDFs: {len(final_files)}")
        
        if new_files:
            total_size = sum(f.stat().st_size for f in new_files)
            print(f"   Total size: {total_size / (1024 * 1024):.1f} MB")
            
            print(f"\n📂 New files exported:")
            for f in new_files[-5:]:  # Show last 5
                size_mb = f.stat().st_size / (1024 * 1024)
                print(f"   - {f.name} ({size_mb:.1f} MB)")
            
            if len(new_files) > 5:
                print(f"   ... and {len(new_files) - 5} more files")
        
        return success
        
    except Exception as e:
        print(f"{Fore.RED}❌ Export failed: {e}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main entry point."""
    success = run_full_export()
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())