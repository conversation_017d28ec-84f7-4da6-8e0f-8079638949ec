#!/usr/bin/env python3
"""Long-running automation script that won't timeout."""

import sys
import time
import signal
import json
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

class LongRunningAutomation:
    """Long-running automation with proper progress tracking."""
    
    def __init__(self):
        self.progress_file = Path("automation_progress.json")
        self.running = True
        self.start_time = time.time()
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, sig, frame):
        """Handle interruption gracefully."""
        print('\n⏹️  Automation interrupted. Saving progress...')
        self.running = False
        self.save_progress("interrupted")
        sys.exit(0)
    
    def save_progress(self, status):
        """Save current progress to file."""
        progress = {
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "start_time": self.start_time,
            "duration": time.time() - self.start_time
        }
        
        with open(self.progress_file, 'w') as f:
            json.dump(progress, f, indent=2)
    
    def load_progress(self):
        """Load previous progress if exists."""
        if self.progress_file.exists():
            with open(self.progress_file, 'r') as f:
                return json.load(f)
        return None
    
    def print_status(self, message):
        """Print status with timestamp."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def monitor_downloads(self):
        """Monitor downloads folder for new files."""
        downloads_dir = Path("downloads/trendinvestor")
        downloads_dir.mkdir(parents=True, exist_ok=True)
        
        initial_files = set(downloads_dir.glob("*.pdf"))
        self.print_status(f"Starting with {len(initial_files)} existing PDFs")
        
        return initial_files
    
    def run_automation(self):
        """Run the long-running automation."""
        try:
            from patreon_automation import PatreonAutomation
            
            # Check previous progress
            prev_progress = self.load_progress()
            if prev_progress:
                self.print_status(f"Previous run: {prev_progress['status']} at {prev_progress['timestamp']}")
            
            # Monitor initial state
            initial_files = self.monitor_downloads()
            
            self.print_status("🚀 Starting FULL automation for trendinvestorhk")
            self.print_status("📋 This will load ALL posts and export each one")
            self.print_status("⚠️  This process may take several hours!")
            self.print_status("💡 You can safely Ctrl+C to stop and resume later")
            self.print_status("")
            
            self.save_progress("starting")
            
            # Initialize automation with custom timeouts
            automation = PatreonAutomation()
            
            # Increase timeouts in config
            automation.config.set('browser.timeout', 300000)  # 5 minutes
            automation.config.set('automation.export_timeout', 600)  # 10 minutes per export
            
            self.print_status("🔗 Connecting to Chrome session...")
            
            # Track progress with periodic updates
            last_check_time = time.time()
            check_interval = 30  # Check every 30 seconds
            
            def progress_callback():
                """Called periodically to show progress."""
                nonlocal last_check_time
                current_time = time.time()
                
                if current_time - last_check_time > check_interval:
                    # Check for new files
                    current_files = set(Path("downloads/trendinvestor").glob("*.pdf"))
                    new_files = current_files - initial_files
                    
                    elapsed = current_time - self.start_time
                    self.print_status(f"⏱️  Running for {elapsed/60:.1f} minutes - {len(new_files)} new PDFs exported")
                    
                    # Update progress
                    self.save_progress(f"running - {len(new_files)} exported")
                    last_check_time = current_time
            
            # Run the automation
            self.print_status("🎯 Starting automation process...")
            
            success = automation.run(
                creator_name="trendinvestorhk",
                max_posts=None,  # No limit
                skip_existing=True
            )
            
            # Final results
            final_files = set(Path("downloads/trendinvestor").glob("*.pdf"))
            new_files = final_files - initial_files
            total_time = time.time() - self.start_time
            
            self.print_status("")
            self.print_status("🎉 Automation completed!")
            self.print_status(f"📊 Results:")
            self.print_status(f"   • New PDFs exported: {len(new_files)}")
            self.print_status(f"   • Total PDFs: {len(final_files)}")
            self.print_status(f"   • Total time: {total_time/60:.1f} minutes")
            
            if new_files:
                total_size = sum(f.stat().st_size for f in new_files)
                self.print_status(f"   • Total size: {total_size / (1024 * 1024):.1f} MB")
            
            status = "completed_success" if success else "completed_with_errors"
            self.save_progress(status)
            
            return success
            
        except Exception as e:
            self.print_status(f"❌ Error: {e}")
            self.save_progress(f"error: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def show_final_summary(self):
        """Show final summary of exported files."""
        downloads_dir = Path("downloads/trendinvestor")
        if not downloads_dir.exists():
            return
        
        pdf_files = list(downloads_dir.glob("*.pdf"))
        if not pdf_files:
            return
        
        self.print_status("")
        self.print_status("📂 All exported PDFs:")
        
        # Sort by modification time (newest first)
        pdf_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
        
        for i, pdf_file in enumerate(pdf_files, 1):
            size_mb = pdf_file.stat().st_size / (1024 * 1024)
            mod_time = datetime.fromtimestamp(pdf_file.stat().st_mtime).strftime("%m/%d %H:%M")
            name = pdf_file.name
            
            # Truncate long names
            if len(name) > 60:
                name = name[:57] + "..."
            
            self.print_status(f"   {i:2d}. {name} ({size_mb:.1f} MB) - {mod_time}")

def main():
    """Main entry point."""
    automation = LongRunningAutomation()
    
    try:
        success = automation.run_automation()
        automation.show_final_summary()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        automation.print_status("⏹️  Automation stopped by user")
        return 130
    except Exception as e:
        automation.print_status(f"❌ Fatal error: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())