#!/usr/bin/env python3
"""Monitor automation progress by checking downloads folder."""

import time
import os
from pathlib import Path

def monitor_downloads():
    """Monitor the downloads folder for new files."""
    downloads_dir = Path("downloads/trendinvestor")
    downloads_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 Monitoring: {downloads_dir.absolute()}")
    print("Looking for new PDF files...")
    print("Press Ctrl+C to stop monitoring\n")
    
    initial_files = set(downloads_dir.glob("*.pdf"))
    print(f"Initial PDF files: {len(initial_files)}")
    
    if initial_files:
        for f in initial_files:
            size_mb = f.stat().st_size / (1024 * 1024)
            print(f"  - {f.name} ({size_mb:.1f} MB)")
    
    print("\n" + "="*50)
    
    try:
        while True:
            current_files = set(downloads_dir.glob("*.pdf"))
            new_files = current_files - initial_files
            
            if new_files:
                print(f"\n🎉 NEW FILES DETECTED:")
                for f in new_files:
                    size_mb = f.stat().st_size / (1024 * 1024)
                    print(f"  ✅ {f.name} ({size_mb:.1f} MB)")
                
                initial_files = current_files
            
            # Check if any files are being written (size changing)
            for f in current_files:
                try:
                    size1 = f.stat().st_size
                    time.sleep(1)
                    size2 = f.stat().st_size
                    
                    if size2 > size1:
                        size_mb = size2 / (1024 * 1024)
                        print(f"📥 Downloading: {f.name} ({size_mb:.1f} MB)")
                except:
                    pass
            
            time.sleep(2)
            
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring stopped")
        
        final_files = set(downloads_dir.glob("*.pdf"))
        total_new = final_files - initial_files
        
        if total_new:
            print(f"\n📊 Final Summary:")
            print(f"Total new files: {len(total_new)}")
            total_size = sum(f.stat().st_size for f in total_new)
            print(f"Total size: {total_size / (1024 * 1024):.1f} MB")

if __name__ == '__main__':
    monitor_downloads()