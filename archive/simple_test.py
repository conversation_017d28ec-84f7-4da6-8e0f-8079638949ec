#!/usr/bin/env python3
"""Simple test to validate basic Playwright functionality."""

import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_basic_browser():
    """Test basic browser automation without extensions."""
    print("🧪 Testing basic browser automation...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            print("Launching browser...")
            browser = p.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()
            
            print("Navigating to Patreon...")
            page.goto("https://www.patreon.com", timeout=30000)
            
            print("Checking page title...")
            title = page.title()
            print(f"Page title: {title}")
            
            print("Waiting 3 seconds...")
            time.sleep(3)
            
            browser.close()
            print("✅ Basic browser test completed successfully!")
            return True
            
    except Exception as e:
        print(f"❌ Browser test failed: {e}")
        return False

def test_creator_page():
    """Test navigating to the specific creator page."""
    print("\n🧪 Testing creator page navigation...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()
            
            creator_url = "https://www.patreon.com/c/trendinvestorhk/posts"
            print(f"Navigating to: {creator_url}")
            
            page.goto(creator_url, timeout=60000)
            
            print("Checking page content...")
            title = page.title()
            print(f"Page title: {title}")
            
            # Check if we can find any post links
            post_links = page.query_selector_all('a[href*="/posts/"]')
            print(f"Found {len(post_links)} potential post links")
            
            print("Waiting 5 seconds for you to see the page...")
            time.sleep(5)
            
            browser.close()
            print("✅ Creator page test completed!")
            return True
            
    except Exception as e:
        print(f"❌ Creator page test failed: {e}")
        return False

def main():
    """Run tests."""
    print("🚀 Running Simple Browser Tests")
    print("=" * 50)
    
    tests = [
        test_basic_browser,
        test_creator_page
    ]
    
    for test in tests:
        try:
            result = test()
            if not result:
                print("❌ Test failed, stopping...")
                return 1
        except KeyboardInterrupt:
            print("\n⏹️ Tests interrupted by user")
            return 130
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            return 1
    
    print("\n🎉 All tests completed successfully!")
    print("\nNote: The automation needs to work with your installed extension.")
    print("You may need to manually log into Patreon in the browser session.")
    return 0

if __name__ == '__main__':
    sys.exit(main())