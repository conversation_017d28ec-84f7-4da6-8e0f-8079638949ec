#!/usr/bin/env python3
"""Monitor the automation progress in real-time."""

import time
import os
from pathlib import Path

def monitor_automation():
    """Monitor automation progress by checking logs and downloads."""
    downloads_dir = Path("downloads/trendinvestor")
    logs_dir = Path("logs")
    log_file = logs_dir / "automation.log"
    
    print("📊 Monitoring Patreon Automation Progress")
    print("=" * 50)
    print(f"Downloads: {downloads_dir.absolute()}")
    print(f"Log file: {log_file.absolute()}")
    print("\nPress Ctrl+C to stop monitoring\n")
    
    # Track initial state
    initial_files = set(downloads_dir.glob("*.pdf")) if downloads_dir.exists() else set()
    last_log_size = log_file.stat().st_size if log_file.exists() else 0
    
    print(f"📁 Initial PDF files: {len(initial_files)}")
    for f in initial_files:
        size_mb = f.stat().st_size / (1024 * 1024)
        print(f"  - {f.name} ({size_mb:.1f} MB)")
    
    print("\n🔍 Monitoring for new activity...")
    print("-" * 50)
    
    try:
        while True:
            # Check for new PDF files
            current_files = set(downloads_dir.glob("*.pdf")) if downloads_dir.exists() else set()
            new_files = current_files - initial_files
            
            if new_files:
                print(f"\n🎉 NEW PDF EXPORTED:")
                for f in new_files:
                    size_mb = f.stat().st_size / (1024 * 1024)
                    print(f"  ✅ {f.name} ({size_mb:.1f} MB)")
                
                initial_files = current_files
                print(f"📊 Total PDFs: {len(current_files)}")
            
            # Check log file for new entries
            if log_file.exists():
                current_log_size = log_file.stat().st_size
                if current_log_size > last_log_size:
                    # Read new log content
                    with open(log_file, 'r', encoding='utf-8') as f:
                        f.seek(last_log_size)
                        new_content = f.read()
                    
                    # Filter and show relevant log lines
                    for line in new_content.strip().split('\n'):
                        if line and any(keyword in line for keyword in [
                            'Currently have', 'posts', 'Found', 'Clicked', 'Starting export',
                            'Exported', 'Success', 'Failed', 'ERROR'
                        ]):
                            # Extract timestamp and message
                            if ' - ' in line:
                                timestamp = line.split(' - ')[0]
                                message = ' - '.join(line.split(' - ')[1:])
                                print(f"📝 {timestamp.split()[-1]} | {message}")
                    
                    last_log_size = current_log_size
            
            time.sleep(3)  # Check every 3 seconds
            
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring stopped")
        
        # Final summary
        final_files = set(downloads_dir.glob("*.pdf")) if downloads_dir.exists() else set()
        total_new = final_files - initial_files
        
        print(f"\n📊 Final Summary:")
        print(f"New PDFs exported: {len(total_new)}")
        print(f"Total PDFs: {len(final_files)}")
        
        if total_new:
            total_size = sum(f.stat().st_size for f in total_new)
            print(f"Total size of new files: {total_size / (1024 * 1024):.1f} MB")

if __name__ == '__main__':
    monitor_automation()