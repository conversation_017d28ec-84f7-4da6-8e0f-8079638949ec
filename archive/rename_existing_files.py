#!/usr/bin/env python3
"""
Rename existing PDF files to include date prefixes based on posts database.
This is a utility script to update files that were downloaded before the date prefix feature.
"""

import sys
import json
import re
from pathlib import Path
from datetime import datetime
import dateutil.parser

def load_posts_database():
    """Load posts database."""
    posts_db_file = Path("posts_database.json")
    if not posts_db_file.exists():
        print("❌ Posts database not found: posts_database.json")
        return None
    
    with open(posts_db_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def format_date_for_filename(publish_date: str) -> str:
    """Convert publish date to YYYYMMDD format for filename."""
    try:
        if not publish_date:
            return "00000000"
        
        parsed_date = dateutil.parser.parse(publish_date)
        return parsed_date.strftime("%Y%m%d")
        
    except Exception as e:
        print(f"⚠️  Could not parse date '{publish_date}': {e}")
        return "00000000"

def sanitize_filename(filename: str) -> str:
    """Remove invalid characters from filename."""
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    filename = re.sub(r'\s+', '_', filename)
    filename = re.sub(r'\.+', '.', filename)
    if len(filename) > 200:
        filename = filename[:200]
    return filename

def find_matching_post(pdf_filename: str, posts_database: dict):
    """Find the post that matches this PDF filename."""
    
    # Remove .pdf extension and clean filename for matching
    pdf_name_clean = pdf_filename.replace('.pdf', '').replace('_', ' ')
    
    # Try to find a post with matching title
    for post in posts_database['posts']:
        post_title = post['title']
        
        # Simple matching - check if significant parts of the title are in filename
        title_words = post_title.replace('丨', ' ').replace('！', ' ').replace('？', ' ').split()
        
        # Count matching words (ignore very short words)
        significant_words = [w for w in title_words if len(w) > 2]
        matches = sum(1 for word in significant_words if word in pdf_name_clean)
        
        # If most significant words match, this is likely the right post
        if len(significant_words) > 0 and matches / len(significant_words) > 0.6:
            return post
    
    return None

def rename_existing_files():
    """Rename existing PDF files with date prefixes."""
    
    print("📝 Renaming Existing Files with Date Prefixes")
    print("=" * 50)
    
    # Load posts database
    posts_database = load_posts_database()
    if not posts_database:
        return
    
    print(f"📖 Loaded {posts_database['total_posts']} posts from database")
    
    # Find PDF files
    downloads_dir = Path("downloads/trendinvestor")
    if not downloads_dir.exists():
        print(f"❌ Downloads directory not found: {downloads_dir}")
        return
    
    pdf_files = list(downloads_dir.glob("*.pdf"))
    if not pdf_files:
        print(f"❌ No PDF files found in {downloads_dir}")
        return
    
    print(f"📁 Found {len(pdf_files)} PDF files to process")
    print()
    
    renamed_count = 0
    already_prefixed_count = 0
    no_match_count = 0
    
    for pdf_file in pdf_files:
        # Skip files that already have date prefix (YYYYMMDD_)
        if re.match(r'^\d{8}_', pdf_file.name):
            already_prefixed_count += 1
            continue
        
        print(f"🔍 Processing: {pdf_file.name}")
        
        # Find matching post
        matching_post = find_matching_post(pdf_file.name, posts_database)
        
        if matching_post:
            # Get date prefix
            date_prefix = format_date_for_filename(matching_post.get('publish_date', ''))
            
            # Create new filename
            original_name = pdf_file.stem
            extension = pdf_file.suffix
            sanitized_name = sanitize_filename(original_name)
            new_filename = f"{date_prefix}_{sanitized_name}{extension}"
            new_path = pdf_file.parent / new_filename
            
            if not new_path.exists():
                try:
                    pdf_file.rename(new_path)
                    print(f"   ✅ Renamed to: {new_path.name}")
                    print(f"   📅 Date: {matching_post.get('publish_date', 'Unknown')}")
                    renamed_count += 1
                except Exception as e:
                    print(f"   ❌ Error renaming: {e}")
            else:
                print(f"   ⚠️  Target file already exists: {new_filename}")
        else:
            print(f"   ❌ No matching post found")
            no_match_count += 1
        
        print()
    
    # Summary
    print("=" * 50)
    print(f"📊 Renaming Summary:")
    print(f"   • Successfully renamed: {renamed_count}")
    print(f"   • Already had date prefix: {already_prefixed_count}")
    print(f"   • No matching post found: {no_match_count}")
    print(f"   • Total files: {len(pdf_files)}")
    
    if renamed_count > 0:
        print(f"\n✅ Successfully renamed {renamed_count} files with date prefixes!")
    
    if no_match_count > 0:
        print(f"\n⚠️  {no_match_count} files could not be matched to posts")
        print(f"   These files may need manual renaming")

def main():
    """Main function."""
    
    print("⚠️  This script will rename your existing PDF files!")
    print("   It will add date prefixes based on the posts database.")
    print("   Example: filename.pdf → 20250715_filename.pdf")
    print()
    
    response = input("Do you want to continue? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ Operation cancelled")
        return
    
    rename_existing_files()

if __name__ == "__main__":
    main()