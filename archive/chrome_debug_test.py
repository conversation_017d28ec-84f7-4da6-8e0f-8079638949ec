#!/usr/bin/env python3
"""Test connecting to existing Chrome instance via debugging port."""

import sys
import time
import subprocess
import requests
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def check_chrome_debug_port():
    """Check if Chrome debug port is available."""
    try:
        response = requests.get("http://localhost:9222/json", timeout=5)
        if response.status_code == 200:
            tabs = response.json()
            print(f"✅ Chrome debug port is active with {len(tabs)} tabs")
            return True
        else:
            print("❌ Chrome debug port responded with error")
            return False
    except Exception as e:
        print(f"❌ Chrome debug port not available: {e}")
        return False

def start_chrome_with_debug():
    """Instructions to start Chrome with debugging enabled."""
    print("\n📋 To enable Chrome debugging, please:")
    print("1. Close all Chrome windows")
    print("2. Run this command in Terminal:")
    print('   /Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome-debug"')
    print("3. Or on Linux/Windows, use: chrome --remote-debugging-port=9222")
    print("4. Navigate to https://www.patreon.com and log in")
    print("5. Then run this test again")

def test_connect_to_chrome():
    """Test connecting to existing Chrome via debug port."""
    print("🧪 Testing connection to existing Chrome...")
    
    if not check_chrome_debug_port():
        start_chrome_with_debug()
        return False
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # Connect to existing Chrome instance
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            
            # Get the first context (existing browser session)
            contexts = browser.contexts
            if not contexts:
                print("❌ No browser contexts found")
                return False
            
            context = contexts[0]
            pages = context.pages
            
            if not pages:
                # Create a new page
                page = context.new_page()
            else:
                # Use existing page
                page = pages[0]
            
            print(f"Connected to Chrome! Current URL: {page.url}")
            
            # Navigate to creator page
            creator_url = "https://www.patreon.com/c/trendinvestorhk/posts"
            print(f"Navigating to: {creator_url}")
            
            page.goto(creator_url, timeout=30000)
            
            # Wait for page to load
            time.sleep(3)
            
            title = page.title()
            print(f"Page title: {title}")
            
            # Look for post links
            post_links = page.query_selector_all('a[href*="/posts/"]')
            print(f"Found {len(post_links)} potential post links")
            
            # Look for the extension button
            extension_button = page.query_selector('#patreon-exporter-button')
            if extension_button:
                print("✅ Found Patreon exporter extension button!")
                button_text = extension_button.text_content()
                print(f"Button text: {button_text}")
            else:
                print("❌ Patreon exporter extension button not found")
                print("Make sure your extension is enabled and working on Patreon")
            
            print("Keeping browser open for 10 seconds...")
            time.sleep(10)
            
            browser.close()
            print("✅ Chrome connection test completed!")
            return True
            
    except Exception as e:
        print(f"❌ Chrome connection test failed: {e}")
        return False

def main():
    """Run Chrome debug test."""
    print("🚀 Testing Chrome Debug Connection")
    print("=" * 50)
    print("This test connects to your existing Chrome session")
    print("to work with your installed extension.\n")
    
    success = test_connect_to_chrome()
    
    if success:
        print("\n🎉 Test successful! Your extension is accessible.")
        print("The automation can now work with your installed extension.")
    else:
        print("\n❌ Test failed. Please follow the setup instructions above.")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())