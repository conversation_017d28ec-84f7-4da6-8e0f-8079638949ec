#!/usr/bin/env python3
"""Show the first few posts that would be processed."""

import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def show_posts():
    """Show first few posts to see what will be processed."""
    try:
        from playwright.sync_api import sync_playwright
        from post_extractor import PostExtractor
        import logging
        
        # Set up logging
        logging.basicConfig(level=logging.WARNING)  # Reduce noise
        logger = logging.getLogger(__name__)
        
        with sync_playwright() as p:
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            context = browser.contexts[0]
            pages = context.pages
            page = pages[0] if pages else context.new_page()
            
            post_extractor = PostExtractor(page, logger)
            creator_url = "https://www.patreon.com/c/trendinvestorhk/posts"
            
            print("📄 Loading first few posts...")
            posts = post_extractor.extract_post_urls(creator_url, max_posts=5)
            
            print(f"\nFound {len(posts)} posts:")
            for i, post in enumerate(posts, 1):
                title = post['title']
                print(f"\n{i}. {title}")
                print(f"   URL: {post['url']}")
                print(f"   ID: {post['id']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == '__main__':
    show_posts()