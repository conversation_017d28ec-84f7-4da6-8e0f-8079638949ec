#!/usr/bin/env python3
"""Live monitoring of the long-running automation."""

import time
import json
import os
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>

def monitor_live():
    """Monitor the automation with live updates."""
    print("📊 Live Automation Monitor")
    print("=" * 50)
    print("Press Ctrl+C to stop monitoring (automation will continue)")
    print("")
    
    progress_file = Path("automation_progress.json")
    log_file = Path("automation_output.log")
    downloads_dir = Path("downloads/trendinvestor")
    
    last_log_position = 0
    last_file_count = 0
    start_time = time.time()
    
    try:
        while True:
            current_time = datetime.now()
            print(f"\n🕐 {current_time.strftime('%H:%M:%S')} - Status Check")
            print("-" * 30)
            
            # Check progress file
            if progress_file.exists():
                try:
                    with open(progress_file, 'r') as f:
                        progress = json.load(f)
                    
                    status = progress.get('status', 'unknown')
                    timestamp = progress.get('timestamp', 'unknown')
                    duration = progress.get('duration', 0)
                    
                    print(f"📋 Status: {status}")
                    print(f"⏱️  Duration: {duration/60:.1f} minutes")
                    print(f"🕐 Last update: {timestamp}")
                    
                except Exception as e:
                    print(f"⚠️  Could not read progress: {e}")
            else:
                print("📋 No progress file found")
            
            # Check downloads
            if downloads_dir.exists():
                pdf_files = list(downloads_dir.glob("*.pdf"))
                file_count = len(pdf_files)
                
                print(f"📁 Total PDFs: {file_count}")
                
                if file_count > last_file_count:
                    new_files = file_count - last_file_count
                    print(f"🆕 New files since last check: {new_files}")
                    
                    # Show newest files
                    if pdf_files:
                        newest = max(pdf_files, key=lambda f: f.stat().st_mtime)
                        size_mb = newest.stat().st_size / (1024 * 1024)
                        mod_time = datetime.fromtimestamp(newest.stat().st_mtime)
                        print(f"📄 Latest: {newest.name} ({size_mb:.1f} MB)")
                        print(f"🕐 Created: {mod_time.strftime('%H:%M:%S')}")
                
                last_file_count = file_count
                
                # Calculate total size
                if pdf_files:
                    total_size = sum(f.stat().st_size for f in pdf_files)
                    print(f"💾 Total size: {total_size / (1024 * 1024):.1f} MB")
            else:
                print("📁 Downloads directory not found")
            
            # Check log file for recent activity
            if log_file.exists():
                try:
                    current_size = log_file.stat().st_size
                    if current_size > last_log_position:
                        # Read new content
                        with open(log_file, 'r', encoding='utf-8') as f:
                            f.seek(last_log_position)
                            new_content = f.read()
                        
                        # Show recent log lines
                        recent_lines = [line.strip() for line in new_content.split('\n') 
                                      if line.strip() and not line.startswith('#')]
                        
                        if recent_lines:
                            print(f"📝 Recent activity:")
                            for line in recent_lines[-3:]:  # Show last 3 lines
                                print(f"   {line}")
                        
                        last_log_position = current_size
                    else:
                        print("📝 No new log activity")
                        
                except Exception as e:
                    print(f"⚠️  Could not read log: {e}")
            else:
                print("📝 No log file found")
            
            # Check if automation process is running
            try:
                pid_file = Path("automation.pid")
                if pid_file.exists():
                    with open(pid_file, 'r') as f:
                        pid = f.read().strip()
                    
                    # Check if process is still running
                    try:
                        os.kill(int(pid), 0)  # Send signal 0 to check if process exists
                        print(f"🔄 Process running (PID: {pid})")
                    except (OSError, ValueError):
                        print(f"⚠️  Process not found (PID: {pid}) - may have completed")
                else:
                    print("🔍 No PID file found")
                    
            except Exception:
                print("🔍 Could not check process status")
            
            # Show estimated progress if we can
            running_time = time.time() - start_time
            if file_count > 0 and running_time > 60:  # After 1 minute
                rate = file_count / (running_time / 60)  # files per minute
                print(f"📈 Export rate: {rate:.1f} PDFs/minute")
            
            print("")
            print("💡 Commands:")
            print("   check_progress.py - Quick status")
            print("   tail -f automation_output.log - Live logs")
            print("   pkill -f long_running_export.py - Stop automation")
            
            # Wait before next check
            time.sleep(30)  # Check every 30 seconds
            
    except KeyboardInterrupt:
        print("\n⏹️  Monitoring stopped (automation continues running)")
        print("📋 Final summary:")
        
        if downloads_dir.exists():
            final_files = list(downloads_dir.glob("*.pdf"))
            print(f"📁 Total PDFs exported: {len(final_files)}")
            
            if final_files:
                total_size = sum(f.stat().st_size for f in final_files)
                print(f"💾 Total size: {total_size / (1024 * 1024):.1f} MB")

if __name__ == '__main__':
    monitor_live()