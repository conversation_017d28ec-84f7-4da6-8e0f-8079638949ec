#!/usr/bin/env python3
"""Example usage of the Patreon Automation system."""

import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from patreon_automation import Patreon<PERSON>utomation


def example_basic_usage():
    """Example: Basic usage with default configuration."""
    print("=== Basic Usage Example ===")
    
    # Initialize with default config
    automation = PatreonAutomation()
    
    # Validate configuration first
    errors = automation.config.validate()
    if errors:
        print("Configuration errors found:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    # Run automation for all enabled creators
    success = automation.run()
    return success


def example_specific_creator():
    """Example: Process specific creator with limits."""
    print("=== Specific Creator Example ===")
    
    automation = PatreonAutomation()
    
    # Process only 'trendinvestorhk' creator, max 5 posts
    success = automation.run(
        creator_name="trendinvestorhk",
        max_posts=5,
        skip_existing=True
    )
    return success


def example_custom_config():
    """Example: Using custom configuration file."""
    print("=== Custom Configuration Example ===")
    
    # Create custom config content
    custom_config = """
browser:
  headless: true  # Run in headless mode
  extension_path: "/path/to/your/extension"

export:
  image_quality: "medium"
  include_comments: false
  output_directory: "./custom_downloads"

automation:
  delay_between_posts: 1  # Faster processing
  max_retries: 2

creators:
  - name: "test_creator"
    url: "https://www.patreon.com/c/test_creator/posts"
    enabled: true
    output_subfolder: "test"
"""
    
    # Write custom config
    custom_config_path = Path("config/custom_settings.yaml")
    custom_config_path.parent.mkdir(exist_ok=True)
    with open(custom_config_path, 'w') as f:
        f.write(custom_config)
    
    # Use custom config
    automation = PatreonAutomation(str(custom_config_path))
    
    # Validate and run
    errors = automation.config.validate()
    if errors:
        print("Custom configuration errors:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    success = automation.run()
    return success


def example_programmatic_config():
    """Example: Programmatically modify configuration."""
    print("=== Programmatic Configuration Example ===")
    
    automation = PatreonAutomation()
    
    # Modify settings programmatically
    automation.config.set('browser.headless', True)
    automation.config.set('export.image_quality', 'low')
    automation.config.set('automation.delay_between_posts', 0.5)
    
    # Add a new creator programmatically
    new_creator = {
        'name': 'dynamic_creator',
        'url': 'https://www.patreon.com/c/dynamic_creator/posts',
        'enabled': True,
        'output_subfolder': 'dynamic'
    }
    
    current_creators = automation.config.get('creators', [])
    current_creators.append(new_creator)
    automation.config.set('creators', current_creators)
    
    # Save modified config
    automation.config.save()
    
    # Run with modified settings
    success = automation.run(max_posts=3)
    return success


def example_with_error_handling():
    """Example: Comprehensive error handling."""
    print("=== Error Handling Example ===")
    
    try:
        automation = PatreonAutomation()
        
        # Pre-flight checks
        print("Performing pre-flight checks...")
        
        # Check if extension path exists
        extension_path = automation.config.extension_path
        if not extension_path or not os.path.exists(extension_path):
            print(f"❌ Extension path not found: {extension_path}")
            print("Please build your extension and update the configuration.")
            return False
        
        # Check output directory permissions
        output_dir = Path(automation.config.output_directory)
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
            test_file = output_dir / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()
            print("✅ Output directory is writable")
        except Exception as e:
            print(f"❌ Cannot write to output directory: {e}")
            return False
        
        # Validate configuration
        errors = automation.config.validate()
        if errors:
            print("❌ Configuration validation failed:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        print("✅ Pre-flight checks passed")
        
        # Run automation with error handling
        success = automation.run()
        
        if success:
            print("🎉 Automation completed successfully!")
        else:
            print("❌ Automation completed with errors")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️ Automation interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def main():
    """Run example based on command line argument."""
    if len(sys.argv) < 2:
        print("Usage: python example_usage.py <example_name>")
        print("Available examples:")
        print("  - basic: Basic usage with default configuration")
        print("  - specific: Process specific creator")
        print("  - custom: Use custom configuration file")
        print("  - programmatic: Modify configuration programmatically")
        print("  - error_handling: Comprehensive error handling")
        return 1
    
    example_name = sys.argv[1].lower()
    
    examples = {
        'basic': example_basic_usage,
        'specific': example_specific_creator,
        'custom': example_custom_config,
        'programmatic': example_programmatic_config,
        'error_handling': example_with_error_handling
    }
    
    if example_name not in examples:
        print(f"Unknown example: {example_name}")
        print(f"Available examples: {', '.join(examples.keys())}")
        return 1
    
    print(f"Running example: {example_name}")
    print("-" * 50)
    
    success = examples[example_name]()
    
    print("-" * 50)
    if success:
        print("Example completed successfully!")
        return 0
    else:
        print("Example failed!")
        return 1


if __name__ == '__main__':
    sys.exit(main())