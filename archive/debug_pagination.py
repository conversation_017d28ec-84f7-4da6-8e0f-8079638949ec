#!/usr/bin/env python3
"""Debug script to find and test the Load More button."""

import sys
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def debug_load_more():
    """Debug the Load More button functionality."""
    print("🔍 Debugging Load More button...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # Connect to existing Chrome
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            context = browser.contexts[0]
            
            # Use existing page or create new one
            pages = context.pages
            if pages:
                page = pages[0]
            else:
                page = context.new_page()
            
            creator_url = "https://www.patreon.com/c/trendinvestorhk/posts"
            print(f"Navigating to: {creator_url}")
            page.goto(creator_url, timeout=30000)
            page.wait_for_timeout(3000)
            
            # Count initial posts
            initial_posts = page.query_selector_all('a[href*="/posts/"]')
            print(f"Initial posts found: {len(initial_posts)}")
            
            # Scroll to bottom to make Load More button visible
            print("Scrolling to bottom...")
            page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            page.wait_for_timeout(2000)
            
            # Look for Load More button with various strategies
            print("\n🔍 Looking for Load More button...")
            
            # Strategy 1: Text-based selectors
            text_selectors = [
                'button:has-text("Load more")',
                'button:has-text("Show more")',
                'button:has-text("View more")',
                'button:has-text("See more")',
                'button:has-text("加載更多")',  # Chinese
                'button:has-text("显示更多")',  # Chinese
                'button:has-text("更多")',     # Chinese
            ]
            
            for selector in text_selectors:
                buttons = page.query_selector_all(selector)
                if buttons:
                    print(f"  ✅ Found {len(buttons)} button(s) with selector: {selector}")
                    for i, btn in enumerate(buttons):
                        text = btn.text_content()
                        visible = btn.is_visible()
                        enabled = not btn.is_disabled()
                        print(f"    [{i+1}] Text: '{text}', Visible: {visible}, Enabled: {enabled}")
            
            # Strategy 2: All buttons with "load" or "more" text
            print("\n🔍 Checking all buttons for load/more text...")
            all_buttons = page.query_selector_all('button')
            load_more_candidates = []
            
            for i, button in enumerate(all_buttons):
                try:
                    text = (button.text_content() or "").lower().strip()
                    if text and any(keyword in text for keyword in ['load', 'more', '更多', '加载', 'see', 'show', 'view']):
                        visible = button.is_visible()
                        enabled = not button.is_disabled()
                        if visible and enabled:
                            load_more_candidates.append((button, text))
                            print(f"  📋 Candidate {len(load_more_candidates)}: '{text}'")
                except Exception:
                    continue
            
            # Strategy 3: Look at page structure for pagination
            print("\n🔍 Looking for pagination elements...")
            pagination_selectors = [
                '[class*="pagination"]',
                '[class*="load"]',
                '[class*="more"]',
                '[data-*="load"]',
                '[data-*="more"]',
                '[aria-label*="load"]',
                '[aria-label*="more"]'
            ]
            
            for selector in pagination_selectors:
                elements = page.query_selector_all(selector)
                if elements:
                    print(f"  📍 Found {len(elements)} elements with selector: {selector}")
                    for elem in elements[:3]:  # Show first 3
                        try:
                            tag = elem.evaluate('el => el.tagName')
                            text = elem.text_content()[:50] if elem.text_content() else ""
                            print(f"    - {tag}: '{text}'")
                        except Exception:
                            pass
            
            # Strategy 4: Try clicking the most likely candidate
            if load_more_candidates:
                print(f"\n🚀 Testing the most likely Load More button...")
                best_candidate = load_more_candidates[0][0]  # First candidate
                candidate_text = load_more_candidates[0][1]
                
                print(f"Clicking button with text: '{candidate_text}'")
                
                try:
                    best_candidate.click()
                    print("✅ Clicked successfully!")
                    
                    # Wait for new content
                    print("Waiting for new content...")
                    page.wait_for_timeout(5000)
                    
                    # Count posts again
                    new_posts = page.query_selector_all('a[href*="/posts/"]')
                    print(f"Posts after click: {len(new_posts)} (was {len(initial_posts)})")
                    
                    if len(new_posts) > len(initial_posts):
                        print(f"🎉 Success! Loaded {len(new_posts) - len(initial_posts)} more posts")
                    else:
                        print("⚠️ No new posts loaded")
                        
                except Exception as e:
                    print(f"❌ Error clicking button: {e}")
            else:
                print("❌ No Load More button candidates found")
            
            # Show page content around bottom for manual inspection
            print("\n📄 Page content near bottom (for manual inspection):")
            try:
                bottom_content = page.evaluate("""
                    () => {
                        const scrollHeight = document.body.scrollHeight;
                        const windowHeight = window.innerHeight;
                        const startY = scrollHeight - windowHeight * 2; // Last 2 screen heights
                        
                        // Get elements in the bottom area
                        const elements = Array.from(document.querySelectorAll('*')).filter(el => {
                            const rect = el.getBoundingClientRect();
                            return rect.top > startY;
                        });
                        
                        // Find buttons and clickable elements
                        const buttons = elements.filter(el => 
                            el.tagName === 'BUTTON' || 
                            el.tagName === 'A' ||
                            el.onclick ||
                            el.getAttribute('role') === 'button'
                        );
                        
                        return buttons.slice(0, 10).map(btn => ({
                            tag: btn.tagName,
                            text: btn.textContent?.trim().substring(0, 100),
                            class: btn.className,
                            id: btn.id
                        }));
                    }
                """)
                
                print("Clickable elements near bottom:")
                for i, elem in enumerate(bottom_content):
                    print(f"  {i+1}. {elem['tag']}: '{elem['text']}' (class: {elem['class'][:50]})")
                    
            except Exception as e:
                print(f"Error getting bottom content: {e}")
            
            print("\nWaiting 10 seconds for manual inspection...")
            time.sleep(10)
            
            return True
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        return False

def main():
    """Run debug."""
    success = debug_load_more()
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())