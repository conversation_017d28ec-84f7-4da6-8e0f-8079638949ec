#!/usr/bin/env python3
"""
Export posts from organized databases, supporting multiple creators.
This is the main script to export posts as PDFs.
"""

import sys
import json
import time
import re
from pathlib import Path
from datetime import datetime
import dateutil.parser

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

class PostExporter:
    """Handles exporting posts with duplicate prevention for multiple creators."""
    
    def __init__(self, creator_name: str = None):
        from database_manager import DatabaseManager
        
        self.db_manager = DatabaseManager()
        self.creator_name = creator_name
        
        # If no creator specified, try to find the default or single creator
        if not self.creator_name:
            creators = self.db_manager.list_creators()
            if not creators:
                raise ValueError("No creators found in database. Run discover_posts.py first.")
            elif len(creators) == 1:
                self.creator_name = creators[0]['name']
                self.creator_url = creators[0]['url']
                print(f"🎯 Using creator: {self.creator_name}")
            else:
                print("Multiple creators found:")
                for i, creator in enumerate(creators, 1):
                    print(f"   {i}. {creator['name']} ({creator['total_posts']} posts)")
                raise ValueError("Multiple creators found. Specify creator name as argument.")
        else:
            # Find creator URL from name
            creators = self.db_manager.list_creators()
            creator_info = next((c for c in creators if c['name'] == self.creator_name), None)
            if not creator_info:
                raise ValueError(f"Creator '{self.creator_name}' not found in database.")
            self.creator_url = creator_info['url']
        
    def load_databases(self):
        """Load posts database and download tracking."""
        
        # Load posts database
        self.posts_database = self.db_manager.load_discovered_posts(self.creator_url)
        if not self.posts_database:
            raise FileNotFoundError(f"Posts database not found for creator: {self.creator_name}")
        
        print(f"📖 Loaded {self.posts_database['total_posts']} posts from {self.creator_name} database")
        
        # Load download tracking
        self.downloaded_posts = self.db_manager.load_downloaded_posts(self.creator_url)
        print(f"📊 Tracking {self.downloaded_posts['total_downloaded']} downloaded posts")
        
    def find_new_posts(self, max_posts: int = None):
        """Find posts that haven't been downloaded yet."""
        
        all_posts = self.posts_database['posts']
        downloaded_urls = set(self.downloaded_posts['downloaded_posts'].keys())
        
        new_posts = [post for post in all_posts if post['url'] not in downloaded_urls]
        
        if max_posts:
            new_posts = new_posts[:max_posts]
        
        print(f"🔍 Found {len(new_posts)} new posts to export")
        return new_posts
        
    def mark_as_downloaded(self, post_url: str, post_title: str, filename: str, file_size: int):
        """Mark a post as successfully downloaded."""
        
        self.downloaded_posts['downloaded_posts'][post_url] = {
            "title": post_title,
            "filename": filename,
            "file_size_bytes": file_size,
            "downloaded_at": datetime.now().isoformat()
        }
        
        self.downloaded_posts['total_downloaded'] = len(self.downloaded_posts['downloaded_posts'])
        self.downloaded_posts['last_updated'] = datetime.now().isoformat()
        
        self.db_manager.save_downloaded_posts(self.creator_url, self.downloaded_posts)
        
    def _format_date_for_filename(self, publish_date: str) -> str:
        """Convert publish date to YYYYMMDD format for filename."""
        try:
            if not publish_date:
                return "00000000"
            
            # Parse the ISO date string
            parsed_date = dateutil.parser.parse(publish_date)
            return parsed_date.strftime("%Y%m%d")
            
        except Exception as e:
            print(f"⚠️  Could not parse date '{publish_date}': {e}")
            return "00000000"
    
    def _sanitize_filename(self, filename: str) -> str:
        """Remove invalid characters from filename, keeping only % symbol."""
        # Remove/replace invalid filename characters for cross-platform compatibility
        # Keep percentage (%) as the only exception symbol
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)  # Replace filesystem reserved chars
        filename = re.sub(r'[!@#$^&()+=\[\]{};\'",~`]', '_', filename)  # Replace other symbols
        filename = re.sub(r'[｜🔥🏠丨]', '_', filename)  # Replace pipes and emojis
        filename = re.sub(r'\s+', '_', filename)  # Replace multiple spaces with single underscore
        filename = re.sub(r'_+', '_', filename)  # Replace multiple underscores with single
        filename = filename.strip('_')  # Remove leading/trailing underscores
        filename = re.sub(r'\.+', '.', filename)  # Replace multiple dots with single
        # Limit length
        if len(filename) > 200:
            filename = filename[:200]
        return filename
    
    def _rename_downloaded_file(self, original_path: Path, post: dict) -> Path:
        """Rename downloaded file to include date prefix."""
        try:
            # Get date prefix
            date_prefix = self._format_date_for_filename(post.get('publish_date', ''))
            
            # Get original filename without extension
            original_name = original_path.stem
            extension = original_path.suffix
            
            # Sanitize the original name
            sanitized_name = self._sanitize_filename(original_name)
            
            # Create new filename: YYYYMMDD_original_name.pdf
            new_filename = f"{date_prefix}_{sanitized_name}{extension}"
            new_path = original_path.parent / new_filename
            
            # Rename the file
            if not new_path.exists():
                original_path.rename(new_path)
                print(f"📝 Renamed: {original_path.name} → {new_path.name}")
                return new_path
            else:
                print(f"⚠️  File already exists: {new_path.name}")
                return original_path
                
        except Exception as e:
            print(f"⚠️  Could not rename file: {e}")
            return original_path
            
    def export_posts(self, max_posts: int = None):
        """Export new posts."""
        
        print("🚀 Starting Post Export")
        print("=" * 40)
        print(f"Creator: {self.creator_name}")
        
        # Load databases
        self.load_databases()
        
        # Find new posts
        new_posts = self.find_new_posts(max_posts)
        
        if not new_posts:
            print("✅ All posts have been downloaded!")
            return
        
        # Set up export
        try:
            from playwright.sync_api import sync_playwright
            from export_controller import ExportController
            from config import Config
            import logging
            
            logging.basicConfig(level=logging.INFO)
            logger = logging.getLogger(__name__)
            config = Config()
            
            with sync_playwright() as p:
                print("🔗 Connecting to Chrome session...")
                
                # Use CDP connection
                browser = p.chromium.connect_over_cdp("http://localhost:9222")
                
                context = browser.contexts[0]
                page = context.pages[0] if context.pages else context.new_page()
                
                export_controller = ExportController(page, config, logger)
                
                print(f"📦 Starting export of {len(new_posts)} posts\n")
                
                success_count = 0
                error_count = 0
                
                for i, post in enumerate(new_posts, 1):
                    print(f"[{i}/{len(new_posts)}] {post['title'][:60]}...")
                    print(f"Date: {post.get('publish_date', 'No date')} | URL: {post['url']}")
                    
                    try:
                        result = export_controller.export_post(post['url'], self.creator_name)
                        
                        if result and result.get('success', False):
                            # Extract file path from result dictionary
                            original_file_path = Path(result['file_path'])
                            
                            # Rename file with date prefix
                            final_file_path = self._rename_downloaded_file(original_file_path, post)
                            file_size = final_file_path.stat().st_size
                            
                            self.mark_as_downloaded(post['url'], post['title'], final_file_path.name, file_size)
                            
                            print(f"✅ Exported: {final_file_path.name} ({file_size / (1024*1024):.1f} MB)")
                            success_count += 1
                        else:
                            error_message = result.get('error', 'Unknown error') if result else 'No result returned'
                            print(f"❌ Export failed: {error_message}")
                            error_count += 1
                            
                    except Exception as e:
                        print(f"❌ Error: {e}")
                        error_count += 1
                    
                    print(f"⏱️  Waiting 3s...\n")
                    time.sleep(3)
                
                # Summary
                print("=" * 40)
                print(f"🎯 Export Complete:")
                print(f"   • Creator: {self.creator_name}")
                print(f"   • Successful: {success_count}")
                print(f"   • Failed: {error_count}")
                print(f"   • Total downloaded: {self.downloaded_posts['total_downloaded']}")
                
                remaining = self.posts_database['total_posts'] - self.downloaded_posts['total_downloaded']
                print(f"   • Remaining: {remaining}")
                
        except Exception as e:
            print(f"❌ Export error: {e}")

def main():
    """Main function."""
    
    creator_name = None
    max_posts = None
    
    # Parse command line arguments
    args = sys.argv[1:]
    
    for arg in args:
        if arg == "--list":
            # List available creators
            from database_manager import DatabaseManager
            db_manager = DatabaseManager()
            creators = db_manager.list_creators()
            
            if not creators:
                print("❌ No creators found in database. Run discover_posts.py first.")
                sys.exit(1)
            
            print("📊 Available Creators:")
            for creator in creators:
                print(f"   • {creator['name']}: {creator['total_posts']} posts, {creator['downloaded_posts']} downloaded")
            return
        elif arg.startswith("--creator="):
            creator_name = arg.split("=", 1)[1]
        else:
            try:
                max_posts = int(arg)
                print(f"📝 Limiting export to {max_posts} posts")
            except ValueError:
                # Assume it's a creator name
                creator_name = arg
    
    if max_posts:
        print(f"📝 Limiting export to {max_posts} posts")
    
    # Create exporter and run
    try:
        exporter = PostExporter(creator_name)
        exporter.export_posts(max_posts)
    except ValueError as e:
        print(f"❌ {e}")
        print("💡 Use --list to see available creators")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()