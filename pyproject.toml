[project]
name = "patreon-automation"
version = "0.1.0"
description = "Automated batch export of Patreon posts using browser automation"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "playwright>=1.40.0",
    "pyyaml>=6.0",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "colorama>=0.4.6",
    "python-dateutil>=2.8.2"
]

[project.scripts]
patreon-automation = "patreon_automation:main"
