#!/usr/bin/env python3
"""
Discover and save posts from a Patreon creator to organized JSON databases.
Supports incremental discovery and multi-creator management.
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def discover_posts(creator_url: str = "https://www.patreon.com/c/trendinvestorhk/posts", 
                  max_posts: int = None, 
                  force_full: bool = False,
                  incremental: bool = True):
    """
    Discover posts from a creator and save to organized JSON database.
    
    Args:
        creator_url: URL to the creator's posts page
        max_posts: Maximum number of posts (None = currently loaded posts only)
        force_full: Force full discovery, ignoring existing database
        incremental: Use incremental discovery (only new posts)
    """
    
    from database_manager import DatabaseManager
    
    db_manager = DatabaseManager()
    creator_name = db_manager._extract_creator_name(creator_url)
    
    print("🔍 Discovering Posts from Patreon Creator")
    print("=" * 50)
    print(f"Creator: {creator_name}")
    print(f"Creator URL: {creator_url}")
    if max_posts is None:
        print(f"Max posts: Currently loaded posts only")
    else:
        print(f"Max posts: {max_posts} (using pagination)")
    
    # Check if we need to discover
    if incremental and not force_full:
        if not db_manager.check_needs_discovery(creator_url, force_full):
            print("✅ Existing database is up to date, no discovery needed")
            print("💡 Use --force to force full rediscovery")
            return db_manager.load_discovered_posts(creator_url)
    
    # Load existing database for incremental discovery
    existing_db = db_manager.load_discovered_posts(creator_url) if incremental and not force_full else None
    
    if existing_db:
        print(f"📚 Found existing database with {existing_db.get('total_posts', 0)} posts")
        latest_post = db_manager.get_latest_post_info(creator_url)
        if latest_post:
            print(f"📅 Latest post: {latest_post['title'][:50]}... ({latest_post.get('publish_date', 'No date')})")
    
    discovery_mode = "incremental" if (incremental and not force_full and existing_db) else "full"
    print(f"🔄 Discovery mode: {discovery_mode}")
    print()
    
    try:
        from playwright.sync_api import sync_playwright
        from post_extractor import PostExtractor
        import logging
        
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        with sync_playwright() as p:
            print("🔗 Connecting to Chrome session...")
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            context = browser.contexts[0]
            pages = context.pages
            page = pages[0] if pages else context.new_page()
            
            # Check if we're already on the right page
            current_url = page.url
            if "patreon.com" in current_url and "posts" in current_url:
                print(f"✅ Already on Patreon posts page")
            else:
                print(f"🔄 Navigating to: {creator_url}")
                try:
                    page.goto(creator_url, wait_until='domcontentloaded', timeout=60000)
                    page.wait_for_timeout(5000)
                except Exception as nav_error:
                    print(f"⚠️  Navigation timeout, but continuing with current page...")
                    # Continue anyway if we're on a Patreon page
                    if "patreon.com" not in page.url:
                        raise nav_error
            
            print(f"📄 Extracting posts with dates...")
            start_time = time.time()
            
            post_extractor = PostExtractor(page, logger)
            
            if max_posts is None:
                # Get currently loaded posts only (fast)
                posts = post_extractor._extract_current_posts()
                method = "current posts"
            else:
                # Use full extraction with pagination (slower)
                posts = post_extractor.extract_post_urls(creator_url, max_posts=max_posts)
                method = f"pagination (max {max_posts})"
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ Discovered {len(posts)} posts using {method} in {duration:.1f}s")
            
            # Handle incremental vs full discovery
            if discovery_mode == "incremental" and existing_db:
                # Merge with existing database
                posts_database = db_manager.merge_new_posts(creator_url, posts, existing_db)
                posts_database['discovery_method'] = f"incremental_{method}"
                posts_database['discovery_duration_seconds'] = duration
            else:
                # Create new database
                posts_with_dates = [p for p in posts if p.get('publish_date')]
                
                posts_database = {
                    "creator_url": creator_url,
                    "total_posts": len(posts),
                    "posts_with_dates": len(posts_with_dates),
                    "date_extraction_success_rate": len(posts_with_dates)/len(posts)*100 if posts else 0,
                    "discovered_at": datetime.now().isoformat(),
                    "discovery_method": f"full_{method}",
                    "discovery_duration_seconds": duration,
                    "posts": posts
                }
            
            # Save to organized database
            db_path = db_manager.get_discovered_db_path(creator_url)
            success = db_manager.save_discovered_posts(creator_url, posts_database)
            
            if success:
                print(f"💾 Database saved: {db_path} ({db_path.stat().st_size / 1024:.1f} KB)")
                
                # Analyze dates
                posts_with_dates = [p for p in posts_database['posts'] if p.get('publish_date')]
                print(f"📅 Date extraction: {len(posts_with_dates)}/{posts_database['total_posts']} ({posts_database['date_extraction_success_rate']:.1f}%) success")
                
                # Show sample posts
                print(f"\n📋 Sample Posts:")
                for i, post in enumerate(posts_database['posts'][:3], 1):
                    print(f"   {i}. {post['title'][:50]}...")
                    print(f"      Date: {post.get('publish_date', 'No date')}")
                
                print(f"\n🎯 Next: Run 'uv run python export_posts.py {creator_name}' to start exporting")
            else:
                print(f"❌ Failed to save database")
                return None
            
            return posts_database
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def main():
    """Main function."""
    
    creator_url = "https://www.patreon.com/c/trendinvestorhk/posts"
    max_posts = 400  # Default to reasonable number
    force_full = False
    incremental = True
    
    # Parse command line arguments
    args = sys.argv[1:]
    
    for arg in args:
        if arg == "--current":
            max_posts = None  # Only currently loaded posts (fast)
            print("📄 Using currently loaded posts only (fast)")
        elif arg == "--all":
            max_posts = 1000  # Large number for pagination  
            print("📄 Using pagination to discover ALL posts (may take 10+ minutes)")
        elif arg == "--force":
            force_full = True
            print("🔄 Force full rediscovery enabled")
        elif arg == "--no-incremental":
            incremental = False
            print("🚫 Incremental discovery disabled")
        elif arg.startswith("--creator="):
            creator_url = arg.split("=", 1)[1]
            print(f"🎯 Using custom creator URL: {creator_url}")
        else:
            try:
                max_posts = int(arg)
                print(f"📄 Using pagination to discover up to {max_posts} posts")
            except ValueError:
                print("❌ Usage: python discover_posts.py [--current | --all | max_posts] [--force] [--no-incremental] [--creator=URL]")
                print("   --current: Only currently loaded posts (fast)")
                print("   --all: All posts using pagination (slow)")
                print("   --force: Force full rediscovery")
                print("   --no-incremental: Disable incremental discovery")
                print("   --creator=URL: Custom creator URL")
                sys.exit(1)
    
    if not force_full and incremental:
        print("📄 Using incremental discovery (only new posts)")
    
    # Discover posts
    result = discover_posts(creator_url, max_posts, force_full, incremental)
    
    if result:
        from database_manager import DatabaseManager
        db_manager = DatabaseManager()
        creator_name = db_manager._extract_creator_name(creator_url)
        
        print(f"\n✅ Success! Found {result['total_posts']} posts for {creator_name}")
        
        # Show database summary
        print(f"\n📊 Database Summary:")
        creators = db_manager.list_creators()
        for creator in creators:
            print(f"   • {creator['name']}: {creator['total_posts']} posts, {creator['downloaded_posts']} downloaded")
    else:
        print(f"\n❌ Failed to discover posts")
        sys.exit(1)

if __name__ == "__main__":
    main()