#!/usr/bin/env python3
"""
Database status and management utility for multiple creators.
"""

import sys
from pathlib import Path
from datetime import datetime
import dateutil.parser

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def show_database_status():
    """Show status of all creator databases."""
    from database_manager import DatabaseManager
    
    db_manager = DatabaseManager()
    creators = db_manager.list_creators()
    
    if not creators:
        print("❌ No creators found in database.")
        print("💡 Run 'uv run python discover_posts.py' to discover posts first.")
        return
    
    print("📊 Database Status")
    print("=" * 60)
    
    for creator in creators:
        print(f"\n🎯 Creator: {creator['name']}")
        print(f"   URL: {creator['url']}")
        print(f"   Total Posts: {creator['total_posts']}")
        print(f"   Downloaded: {creator['downloaded_posts']}")
        print(f"   Remaining: {creator['total_posts'] - creator['downloaded_posts']}")
        
        # Calculate progress percentage
        if creator['total_posts'] > 0:
            progress = (creator['downloaded_posts'] / creator['total_posts']) * 100
            print(f"   Progress: {progress:.1f}%")
        
        # Show last update time
        if creator['discovered_at']:
            try:
                discovered_at = dateutil.parser.parse(creator['discovered_at'])
                age = datetime.now() - discovered_at.replace(tzinfo=None)
                age_str = f"{age.days} days ago" if age.days > 0 else f"{age.seconds // 3600} hours ago"
                print(f"   Last Discovery: {age_str}")
            except Exception:
                print(f"   Last Discovery: {creator['discovered_at']}")
        
        if creator['last_update'] and creator['last_update'] != creator['discovered_at']:
            try:
                last_update = dateutil.parser.parse(creator['last_update'])
                age = datetime.now() - last_update.replace(tzinfo=None)
                age_str = f"{age.days} days ago" if age.days > 0 else f"{age.seconds // 3600} hours ago"
                print(f"   Last Update: {age_str}")
            except Exception:
                print(f"   Last Update: {creator['last_update']}")
    
    print("\n📁 Database Files:")
    print(f"   Discovered: {db_manager.discovered_dir}")
    print(f"   Downloaded: {db_manager.downloaded_dir}")

def migrate_old_databases():
    """Migrate old single-file databases to new organized structure."""
    from database_manager import DatabaseManager
    
    db_manager = DatabaseManager()
    
    # Check for old files
    old_posts_db = Path("posts_database.json")
    old_downloaded_db = Path("downloaded_posts.json")
    
    if not old_posts_db.exists() and not old_downloaded_db.exists():
        print("✅ No old databases found to migrate.")
        return
    
    print("🔄 Migrating old databases to new structure...")
    
    # Default creator URL for old databases
    creator_url = "https://www.patreon.com/c/trendinvestorhk/posts"
    
    # Migrate posts database
    if old_posts_db.exists():
        print(f"📚 Migrating {old_posts_db}...")
        
        try:
            import json
            with open(old_posts_db, 'r', encoding='utf-8') as f:
                old_data = json.load(f)
            
            # Use the creator URL from the old database if available
            if 'creator_url' in old_data:
                creator_url = old_data['creator_url']
            
            success = db_manager.save_discovered_posts(creator_url, old_data)
            
            if success:
                new_path = db_manager.get_discovered_db_path(creator_url)
                print(f"✅ Migrated to {new_path}")
                
                # Backup old file
                backup_path = old_posts_db.with_suffix('.json.backup')
                old_posts_db.rename(backup_path)
                print(f"📦 Backed up old file to {backup_path}")
            else:
                print(f"❌ Failed to migrate {old_posts_db}")
                
        except Exception as e:
            print(f"❌ Error migrating {old_posts_db}: {e}")
    
    # Migrate downloaded database
    if old_downloaded_db.exists():
        print(f"📥 Migrating {old_downloaded_db}...")
        
        try:
            import json
            with open(old_downloaded_db, 'r', encoding='utf-8') as f:
                old_data = json.load(f)
            
            # Add creator URL if not present
            if 'creator_url' not in old_data:
                old_data['creator_url'] = creator_url
            
            success = db_manager.save_downloaded_posts(creator_url, old_data)
            
            if success:
                new_path = db_manager.get_downloaded_db_path(creator_url)
                print(f"✅ Migrated to {new_path}")
                
                # Backup old file
                backup_path = old_downloaded_db.with_suffix('.json.backup')
                old_downloaded_db.rename(backup_path)
                print(f"📦 Backed up old file to {backup_path}")
            else:
                print(f"❌ Failed to migrate {old_downloaded_db}")
                
        except Exception as e:
            print(f"❌ Error migrating {old_downloaded_db}: {e}")
    
    print("\n✅ Migration complete!")
    print("💡 Use 'uv run python database_status.py' to see the new structure")

def main():
    """Main function."""
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--migrate":
            migrate_old_databases()
        elif sys.argv[1] == "--help":
            print("Database Status and Management Utility")
            print("Usage:")
            print("  python database_status.py          # Show database status")
            print("  python database_status.py --migrate # Migrate old databases")
            print("  python database_status.py --help    # Show this help")
        else:
            print("❌ Unknown argument. Use --help for usage.")
            sys.exit(1)
    else:
        show_database_status()

if __name__ == "__main__":
    main()