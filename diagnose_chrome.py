#!/usr/bin/env python3
"""Diagnose Chrome and extension status."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def diagnose_chrome_status():
    """Check Chrome browser and extension status."""
    
    print("🔍 Chrome & Extension Diagnosis")
    print("=" * 40)
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            print("🔗 Connecting to Chrome session...")
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            context = browser.contexts[0]
            page = context.pages[0] if context.pages else context.new_page()
            
            # Check current page
            print(f"📄 Current URL: {page.url}")
            print(f"📄 Page title: '{page.title()}'")
            
            # Check if we're on Cloudflare loading page
            if "Just a moment" in page.title() or "checking your browser" in page.title().lower():
                print("❌ Chrome is stuck on Cloudflare loading page!")
                print("   Please manually refresh the page and navigate to:")
                print("   https://www.patreon.com/c/trendinvestorhk/posts")
                return False
            
            # Check if we're on Patreon
            if "patreon.com" not in page.url:
                print("⚠️  Not on Patreon. Navigating...")
                try:
                    page.goto("https://www.patreon.com/c/trendinvestorhk/posts", timeout=30000)
                    print(f"✅ Navigated to: {page.url}")
                except Exception as e:
                    print(f"❌ Navigation failed: {e}")
                    return False
            
            # Check for posts on the page
            print("🔍 Checking for posts on page...")
            post_links = page.query_selector_all('a[href*="/posts/"]')
            print(f"   Found {len(post_links)} post links")
            
            if len(post_links) == 0:
                print("❌ No posts found on page!")
                print("   The page may not have loaded properly")
                return False
            
            # Check for Patreon export extension
            print("🔍 Checking for export extension...")
            
            # Look for common extension UI elements
            export_buttons = page.query_selector_all('button:has-text("Export")')
            download_buttons = page.query_selector_all('button:has-text("Download")')
            print(f"   Found {len(export_buttons)} 'Export' buttons")
            print(f"   Found {len(download_buttons)} 'Download' buttons")
            
            if len(export_buttons) == 0 and len(download_buttons) == 0:
                print("⚠️  No export buttons found!")
                print("   The Patreon export extension may not be working")
                print("   Try:")
                print("   1. Refresh the page")
                print("   2. Check if extension is enabled")
                print("   3. Navigate to a specific post to test")
                return False
            
            print("✅ Chrome and extension appear to be working!")
            return True
            
    except Exception as e:
        print(f"❌ Error connecting to Chrome: {e}")
        print("   Make sure Chrome is running with --remote-debugging-port=9222")
        return False

def test_single_post_export():
    """Test export on a single post."""
    
    print(f"\n🧪 Testing Single Post Export")
    print("-" * 30)
    
    try:
        from playwright.sync_api import sync_playwright
        from export_controller import ExportController
        from config import Config
        import logging
        
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        config = Config()
        
        with sync_playwright() as p:
            browser = p.chromium.connect_over_cdp("http://localhost:9222")
            context = browser.contexts[0]
            page = context.pages[0] if context.pages else context.new_page()
            
            # Navigate to first post
            test_url = "https://www.patreon.com/posts/pin-dao-zhu-ye-107727637"
            print(f"🔄 Navigating to test post: {test_url}")
            
            page.goto(test_url, timeout=30000)
            page.wait_for_timeout(3000)
            
            print(f"📄 On page: {page.title()}")
            
            # Look for extension buttons
            export_buttons = page.query_selector_all('button:has-text("Export")')
            download_buttons = page.query_selector_all('button:has-text("Download")')
            
            print(f"🔍 Found {len(export_buttons)} 'Export' buttons")
            print(f"🔍 Found {len(download_buttons)} 'Download' buttons")
            
            if len(export_buttons) > 0 or len(download_buttons) > 0:
                print("✅ Extension UI elements found!")
                print("   The export should work")
            else:
                print("❌ No extension UI found!")
                print("   Check if extension is loaded and working")
            
    except Exception as e:
        print(f"❌ Error testing export: {e}")

def main():
    """Main function."""
    
    # Check Chrome status
    chrome_ok = diagnose_chrome_status()
    
    if chrome_ok:
        # Test export functionality
        test_single_post_export()
        
        print(f"\n🎯 Next Steps:")
        print(f"   1. If everything looks good, try: uv run python export_posts.py 1")
        print(f"   2. If export still fails, manually test the extension on a post")
        print(f"   3. Check Downloads folder for new PDFs after manual test")
    else:
        print(f"\n🚨 Fix Chrome/Extension Issues First:")
        print(f"   1. Refresh Chrome page")
        print(f"   2. Navigate to: https://www.patreon.com/c/trendinvestorhk/posts")
        print(f"   3. Verify extension is working manually")
        print(f"   4. Then retry: uv run python export_posts.py 1")

if __name__ == "__main__":
    main()