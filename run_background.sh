#!/bin/bash

# Background automation runner script
# This runs the automation in the background without timeouts

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "🚀 Starting Patreon Automation in Background"
echo "============================================"
echo "📁 Working directory: $SCRIPT_DIR"
echo "📝 Logs will be saved to: automation_output.log"
echo "📊 Progress file: automation_progress.json"
echo ""
echo "💡 To monitor progress:"
echo "   tail -f automation_output.log"
echo "   uv run python check_progress.py"
echo ""
echo "⏹️  To stop the automation:"
echo "   pkill -f long_running_export.py"
echo ""

# Create log file
touch automation_output.log

# Run automation in background with output logging
echo "🎯 Starting automation process..."
echo "$(date): Starting Patreon automation" >> automation_output.log

# Use nohup to prevent termination when terminal closes
nohup uv run python long_running_export.py >> automation_output.log 2>&1 &

# Get the process ID
PID=$!
echo "✅ Automation started with PID: $PID"
echo "📝 Output logging to: automation_output.log"

# Save PID for later reference
echo "$PID" > automation.pid

echo ""
echo "🔍 Initial status check (first 10 seconds)..."
sleep 2

# Show initial output
if [ -f automation_output.log ]; then
    echo "📋 Initial output:"
    echo "=================="
    tail -10 automation_output.log
fi

echo ""
echo "✅ Automation is now running in the background!"
echo ""
echo "📋 Useful commands:"
echo "   # Check progress"
echo "   uv run python check_progress.py"
echo ""
echo "   # Monitor live logs"
echo "   tail -f automation_output.log"
echo ""
echo "   # Stop automation"
echo "   kill $PID"
echo "   # or"
echo "   pkill -f long_running_export.py"
echo ""
echo "📊 The automation will continue running even if you close this terminal."
echo "📁 All PDFs will be saved to: downloads/trendinvestor/"