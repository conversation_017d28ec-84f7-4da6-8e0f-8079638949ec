# Patreon Automation Configuration

# Browser Configuration
browser:
  headless: false  # Set to true for headless mode
  timeout: 60000   # Timeout in milliseconds (increased)
  extension_path: ""  # Leave empty to use already installed extension
  user_data_dir: ""  # Leave empty to use fresh browser instance

# Export Settings (matches your extension settings)
export:
  image_quality: "high"  # high, medium, low
  include_comments: true
  show_download_dialog: false
  output_directory: "./downloads"

# Automation Settings
automation:
  delay_between_posts: 3  # Seconds to wait between processing posts
  max_retries: 3          # Maximum retries for failed exports
  scroll_delay: 1         # Seconds to wait for page scrolling
  export_timeout: 180     # Seconds to wait for export completion (increased for large files)

# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "logs/automation.log"

# Creators to process (examples)
creators:
  - name: "trendinvestorhk"
    url: "https://www.patreon.com/c/trendinvestorhk/posts"
    enabled: true
    output_subfolder: "trendinvestor"
    
  # Add more creators as needed
  # - name: "another_creator"
  #   url: "https://www.patreon.com/c/another_creator/posts"
  #   enabled: false
  #   output_subfolder: "another"