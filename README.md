# Patreon Automation

Automated system to export all posts from Patreon creators as PDFs using your existing Chrome extension. Now supports multiple creators with organized databases and smart incremental discovery.

## 🚀 Quick Start

### Prerequisites
- Chrome with Patreon Export extension installed
- Chrome running with debugging port: `--remote-debugging-port=9222`
- Python with `uv` package manager

### Essential Scripts

#### 1. 🔍 Discover Posts
```bash
# Smart incremental discovery (recommended - only discovers new posts)
uv run python discover_posts.py

# Discover up to 400 posts using pagination (default)
uv run python discover_posts.py 400

# Discover ALL posts (may take 10+ minutes)
uv run python discover_posts.py --all

# Discover currently loaded posts only (fast)
uv run python discover_posts.py --current

# Force full rediscovery (ignores existing database)
uv run python discover_posts.py --force

# Discover posts for a different creator
uv run python discover_posts.py --creator=https://www.patreon.com/c/anothercreator/posts
```

The script now features **smart incremental discovery** - it automatically detects if you have an existing database and only discovers new posts unless you use `--force`.

#### 2. 📦 Export Posts  
```bash
# Export all new posts (auto-detects single creator)
uv run python export_posts.py

# Export posts for a specific creator
uv run python export_posts.py trendinvestorhk

# Export only 10 posts for testing
uv run python export_posts.py 10

# List available creators
uv run python export_posts.py --list

# Export posts using creator flag
uv run python export_posts.py --creator=trendinvestorhk
```

#### 3. 📊 Database Status
```bash
# Show database status for all creators
uv run python database_status.py

# Migrate old single-file databases to new organized structure
uv run python database_status.py --migrate
```

#### 4. 🔧 Utilities
```bash
# Check Chrome connection and system status
uv run python check_status.py

# Diagnose Chrome debugging connection
uv run python diagnose_chrome.py
```

## 📁 New Organized Structure

### Database Files
- `database/discovered/` - Discovered posts for each creator
  - `trendinvestorhk.json` - Posts database for trendinvestorhk
  - `anothercreator.json` - Posts database for another creator
- `database/downloaded/` - Download tracking for each creator
  - `trendinvestorhk.json` - Download tracking for trendinvestorhk
  - `anothercreator.json` - Download tracking for another creator

### Output Files
- `downloads/trendinvestorhk/` - Exported PDF files for trendinvestorhk
- `downloads/anothercreator/` - Exported PDF files for another creator

## 🔄 Typical Workflow

### For New Users
1. **Setup**: Start Chrome with debugging port and navigate to creator's posts page
2. **Discover**: `uv run python discover_posts.py` (incremental discovery)
3. **Export**: `uv run python export_posts.py`
4. **Monitor**: `uv run python database_status.py`

### For Existing Users (Migration)
1. **Migrate**: `uv run python database_status.py --migrate` (one-time migration)
2. **Continue**: Normal workflow as above

### For Multiple Creators
1. **Discover Creator A**: `uv run python discover_posts.py --creator=https://www.patreon.com/c/creatorA/posts`
2. **Discover Creator B**: `uv run python discover_posts.py --creator=https://www.patreon.com/c/creatorB/posts`
3. **Export Creator A**: `uv run python export_posts.py creatorA`
4. **Export Creator B**: `uv run python export_posts.py creatorB`

## ⚡ New Features

### Smart Incremental Discovery
- **Automatic Detection**: Checks if database exists and is recent
- **Time-based Refresh**: Automatically refreshes databases older than 24 hours
- **Merge New Posts**: Only discovers and adds new posts to existing database
- **Force Override**: Use `--force` to completely rediscover all posts

### Multi-Creator Support
- **Organized Databases**: Separate databases for each creator
- **Creator Detection**: Automatically extracts creator names from URLs
- **Isolated Tracking**: Each creator has independent download tracking
- **Creator Management**: List, status, and manage multiple creators

### Enhanced Export System
- **Creator-Specific Exports**: Export posts for specific creators
- **Smart Creator Detection**: Auto-detects single creator or prompts for selection
- **Progress Tracking**: Per-creator download progress and statistics
- **Organized Output**: Creator-specific output directories

## 🛠️ Technical Details

### Database Structure
- **Discovered Posts**: `/database/discovered/creatorname.json`
  - Contains all discovered posts with metadata
  - Includes discovery timestamps and method information
  - Supports incremental updates and merging

- **Downloaded Posts**: `/database/downloaded/creatorname.json`
  - Tracks which posts have been successfully downloaded
  - Includes download timestamps and file information
  - Prevents duplicate downloads

### Incremental Discovery Logic
1. **Check Existing Database**: Looks for existing discovered posts
2. **Age Verification**: Checks if database is less than 24 hours old
3. **Latest Post Detection**: Identifies the most recent post in database
4. **New Post Discovery**: Only discovers posts newer than latest known post
5. **Merge Operation**: Combines new posts with existing database

### Export Enhancements
- **Multi-Creator Support**: Handles multiple creators seamlessly
- **Creator-Specific Directories**: Organizes exports by creator
- **Enhanced Filename Sanitization**: Cross-platform compatible filenames
- **Temporary File Cleanup**: Removes playwright artifacts after processing
- **Progress Reporting**: Per-creator statistics and progress tracking

## 📝 Configuration

Main settings are in `config/settings.yaml`:
- Output directory structure
- Export timeouts  
- Creator-specific settings
- Extension configuration

## 🔧 Migration Guide

If you're upgrading from the old single-file system:

1. **Backup**: Your old files will be automatically backed up
2. **Migrate**: Run `uv run python database_status.py --migrate`
3. **Verify**: Check with `uv run python database_status.py`
4. **Continue**: Use the new commands as normal

## 💡 Pro Tips

- **Daily Runs**: Run discovery daily to keep databases fresh
- **Multiple Creators**: Use specific creator URLs to track multiple creators
- **Incremental Updates**: Let the system automatically handle incremental discovery
- **Force Refresh**: Use `--force` only when you suspect data corruption
- **Monitor Progress**: Use `database_status.py` to track progress across all creators

## 🚀 Advanced Usage

### Custom Creator Discovery
```bash
# Discover posts from any creator
uv run python discover_posts.py --creator=https://www.patreon.com/c/yourcreator/posts

# Force full rediscovery for a specific creator
uv run python discover_posts.py --creator=https://www.patreon.com/c/yourcreator/posts --force
```

### Batch Operations
```bash
# Export all creators (if multiple exist)
for creator in $(python -c "from src.database_manager import DatabaseManager; db=DatabaseManager(); [print(c['name']) for c in db.list_creators()]"); do
    uv run python export_posts.py "$creator"
done
```

The system automatically connects to your existing Chrome session and extension installation, now with enhanced multi-creator support and intelligent incremental discovery.