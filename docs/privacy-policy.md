# Privacy Policy for Patreon Exporter

**Last updated: January 2025**

## Overview

Patreon Exporter is a browser extension that helps users export Patreon posts to clean PDF files for offline reading. This privacy policy explains our data practices.

## Data Collection

**We do not collect, store, or transmit any personal data or browsing information.**

The extension operates entirely locally on your device and does not send any data to external servers.

## Local Storage Only

The extension stores only user preferences locally on your device:

- **PDF image quality settings** (high/medium/low)
- **Comment inclusion preferences** (whether to include comments in PDFs)
- **Download dialog preferences** (whether to show save dialog)

This data:

- Never leaves your device
- Is not shared with any third parties
- Is not accessible to us or anyone else
- Can be cleared by uninstalling the extension

## Permissions Explained

The extension requests these permissions for its core functionality:

- **activeTab**: Access the current Patreon page content to extract post text and images for PDF generation
- **downloads**: Save the generated PDF files to your device's download folder
- **storage**: Store your preferences locally in your browser
- **host permissions (patreon.com)**: Access Patreon domains to read the post content you want to export

## Data Processing

When you use the extension:

1. It reads only the current Patreon post you're viewing
2. Processes the content locally in your browser to remove clutter
3. Generates a PDF file on your device
4. Saves the PDF using your browser's download system

**No data is sent to external servers at any point in this process.**

## Third-Party Services

This extension does not integrate with or send data to any third-party services, analytics platforms, or external APIs.

## Changes to This Policy

If we make changes to this privacy policy, we will update the "Last updated" date above. Continued use of the extension after changes constitutes acceptance of the revised policy.

## Contact

For questions about this privacy policy or the extension, you can:

- Create an issue on our [GitHub repository](https://github.com/yourusername/patreon-export)
- Contact us through the Chrome Web Store support page

## Compliance

This extension complies with:

- Chrome Web Store Developer Program Policies
- General Data Protection Regulation (GDPR)
- California Consumer Privacy Act (CCPA)

---

*This privacy policy applies to Patreon Exporter browser extension available on the Chrome Web Store.*
